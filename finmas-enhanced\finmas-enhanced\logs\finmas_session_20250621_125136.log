2025-06-21 12:51:36,349 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_125136
2025-06-21 12:51:36,349 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_125136
2025-06-21 12:51:36,349 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_125136
2025-06-21 12:51:36,349 - finmas_enhanced - INFO - Financial Analyst initialized
2025-06-21 12:51:36,349 - finmas_enhanced - INFO - Financial Analyst initialized
2025-06-21 12:51:36,481 - finmas_enhanced - INFO - Bedrock client initialized with model: us.anthropic.claude-3-5-sonnet-20241022-v2:0
2025-06-21 12:51:36,481 - finmas_enhanced - INFO - Bedrock client initialized with model: us.anthropic.claude-3-5-sonnet-20241022-v2:0
2025-06-21 12:51:38,179 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 16+4=20, Cost: $0.0001
2025-06-21 12:51:38,179 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 16+4=20, Cost: $0.0001
2025-06-21 12:51:38,179 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 12:51:38,179 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 12:51:38,185 - finmas_enhanced - INFO - Agent Coordinator initialized with 6 specialized agents
2025-06-21 12:51:38,185 - finmas_enhanced - INFO - Agent Coordinator initialized with 6 specialized agents
2025-06-21 12:51:38,185 - finmas_enhanced - INFO - All components initialized successfully
2025-06-21 12:51:38,185 - finmas_enhanced - INFO - All components initialized successfully
2025-06-21 12:51:54,743 - finmas_enhanced - INFO - Analyzing query: how did nike performed last year vs puma...
2025-06-21 12:51:54,743 - finmas_enhanced - INFO - Analyzing query: how did nike performed last year vs puma...
2025-06-21 12:51:54,743 - finmas_enhanced - INFO - Analyzing query intent: how did nike performed last year vs puma...
2025-06-21 12:51:54,743 - finmas_enhanced - INFO - Analyzing query intent: how did nike performed last year vs puma...
2025-06-21 12:51:57,204 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 649+101=750, Cost: $0.0035
2025-06-21 12:51:57,204 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 649+101=750, Cost: $0.0035
2025-06-21 12:51:57,204 - finmas_enhanced - INFO - Intent analysis complete: compare, tickers: ['NKE', 'PUMA.DE'], confidence: 0.95
2025-06-21 12:51:57,204 - finmas_enhanced - INFO - Intent analysis complete: compare, tickers: ['NKE', 'PUMA.DE'], confidence: 0.95
2025-06-21 12:51:57,206 - finmas_enhanced - INFO - Intent analysis: compare, tickers: ['NKE', 'PUMA.DE'], confidence: 0.95
2025-06-21 12:51:57,206 - finmas_enhanced - INFO - Intent analysis: compare, tickers: ['NKE', 'PUMA.DE'], confidence: 0.95
2025-06-21 12:51:58,395 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 170+8=178, Cost: $0.0006
2025-06-21 12:51:58,395 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 170+8=178, Cost: $0.0006
2025-06-21 12:51:58,499 - finmas_enhanced - INFO - Starting collaborative analysis for NKE with agents: ['technical', 'fundamental', 'news', 'market', 'risk', 'investment']
2025-06-21 12:51:58,499 - finmas_enhanced - INFO - Starting collaborative analysis for NKE with agents: ['technical', 'fundamental', 'news', 'market', 'risk', 'investment']
2025-06-21 12:51:58,500 - finmas_enhanced - INFO - Technical Analyst analyzing NKE
2025-06-21 12:51:58,500 - finmas_enhanced - INFO - Technical Analyst analyzing NKE
2025-06-21 12:51:58,500 - finmas_enhanced - INFO - Fetching stock data for NKE
2025-06-21 12:51:58,500 - finmas_enhanced - INFO - Fetching stock data for NKE
2025-06-21 12:51:59,243 - finmas_enhanced - INFO - Stock data retrieved for NKE: $59.79
2025-06-21 12:51:59,243 - finmas_enhanced - INFO - Stock data retrieved for NKE: $59.79
2025-06-21 12:51:59,245 - finmas_enhanced - INFO - Calculating technical indicators for NKE
2025-06-21 12:51:59,245 - finmas_enhanced - INFO - Calculating technical indicators for NKE
2025-06-21 12:52:02,418 - finmas_enhanced - INFO - Technical indicators calculated for NKE
2025-06-21 12:52:02,418 - finmas_enhanced - INFO - Technical indicators calculated for NKE
2025-06-21 12:52:02,419 - finmas_enhanced - INFO - Fundamental Analyst analyzing NKE
2025-06-21 12:52:02,419 - finmas_enhanced - INFO - Fundamental Analyst analyzing NKE
2025-06-21 12:52:02,420 - finmas_enhanced - INFO - Fetching stock data for NKE
2025-06-21 12:52:02,420 - finmas_enhanced - INFO - Fetching stock data for NKE
2025-06-21 12:52:02,699 - finmas_enhanced - INFO - Stock data retrieved for NKE: $59.79
2025-06-21 12:52:02,699 - finmas_enhanced - INFO - Stock data retrieved for NKE: $59.79
2025-06-21 12:52:02,704 - finmas_enhanced - INFO - Fetching financial metrics for NKE
2025-06-21 12:52:02,704 - finmas_enhanced - INFO - Fetching financial metrics for NKE
2025-06-21 12:52:03,659 - finmas_enhanced - INFO - Financial metrics retrieved for NKE
2025-06-21 12:52:03,659 - finmas_enhanced - INFO - Financial metrics retrieved for NKE
2025-06-21 12:52:03,660 - finmas_enhanced - ERROR - Fundamental analysis failed for NKE: Invalid format specifier '.1f if stock_data.pe_ratio else 'N/A'' for object of type 'float'
2025-06-21 12:52:03,660 - finmas_enhanced - ERROR - Fundamental analysis failed for NKE: Invalid format specifier '.1f if stock_data.pe_ratio else 'N/A'' for object of type 'float'
2025-06-21 12:52:03,660 - finmas_enhanced - INFO - News Analyst analyzing NKE
2025-06-21 12:52:03,660 - finmas_enhanced - INFO - News Analyst analyzing NKE
2025-06-21 12:52:03,660 - finmas_enhanced - INFO - Fetching news for NKE
2025-06-21 12:52:03,660 - finmas_enhanced - INFO - Fetching news for NKE
2025-06-21 12:52:04,220 - finmas_enhanced - INFO - Retrieved 10 news items for NKE
2025-06-21 12:52:04,220 - finmas_enhanced - INFO - Retrieved 10 news items for NKE
2025-06-21 12:52:04,220 - finmas_enhanced - INFO - Fetching stock data for NKE
2025-06-21 12:52:04,220 - finmas_enhanced - INFO - Fetching stock data for NKE
2025-06-21 12:52:04,449 - finmas_enhanced - INFO - Stock data retrieved for NKE: $59.79
2025-06-21 12:52:04,449 - finmas_enhanced - INFO - Stock data retrieved for NKE: $59.79
2025-06-21 12:52:04,449 - finmas_enhanced - INFO - Market Analyst analyzing NKE
2025-06-21 12:52:04,449 - finmas_enhanced - INFO - Market Analyst analyzing NKE
2025-06-21 12:52:04,449 - finmas_enhanced - INFO - Fetching stock data for NKE
2025-06-21 12:52:04,449 - finmas_enhanced - INFO - Fetching stock data for NKE
2025-06-21 12:52:04,598 - finmas_enhanced - INFO - Stock data retrieved for NKE: $59.79
2025-06-21 12:52:04,598 - finmas_enhanced - INFO - Stock data retrieved for NKE: $59.79
2025-06-21 12:52:04,599 - finmas_enhanced - ERROR - Market analysis failed for NKE: Invalid format specifier '.2f if stock_data.beta else 'N/A'' for object of type 'float'
2025-06-21 12:52:04,599 - finmas_enhanced - ERROR - Market analysis failed for NKE: Invalid format specifier '.2f if stock_data.beta else 'N/A'' for object of type 'float'
2025-06-21 12:52:20,171 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 492+551=1043, Cost: $0.0097
2025-06-21 12:52:20,171 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 492+551=1043, Cost: $0.0097
2025-06-21 12:52:22,134 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 590+832=1422, Cost: $0.0142
2025-06-21 12:52:22,134 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 590+832=1422, Cost: $0.0142
2025-06-21 12:52:22,134 - finmas_enhanced - INFO - Risk Analyst analyzing NKE
2025-06-21 12:52:22,134 - finmas_enhanced - INFO - Risk Analyst analyzing NKE
2025-06-21 12:52:22,138 - finmas_enhanced - INFO - Fetching stock data for NKE
2025-06-21 12:52:22,138 - finmas_enhanced - INFO - Fetching stock data for NKE
2025-06-21 12:52:22,360 - finmas_enhanced - INFO - Stock data retrieved for NKE: $59.79
2025-06-21 12:52:22,360 - finmas_enhanced - INFO - Stock data retrieved for NKE: $59.79
2025-06-21 12:52:22,360 - finmas_enhanced - INFO - Fetching financial metrics for NKE
2025-06-21 12:52:22,360 - finmas_enhanced - INFO - Fetching financial metrics for NKE
2025-06-21 12:52:22,545 - finmas_enhanced - INFO - Financial metrics retrieved for NKE
2025-06-21 12:52:22,545 - finmas_enhanced - INFO - Financial metrics retrieved for NKE
2025-06-21 12:52:22,545 - finmas_enhanced - INFO - Calculating technical indicators for NKE
2025-06-21 12:52:22,545 - finmas_enhanced - INFO - Calculating technical indicators for NKE
2025-06-21 12:52:22,717 - finmas_enhanced - INFO - Technical indicators calculated for NKE
2025-06-21 12:52:22,717 - finmas_enhanced - INFO - Technical indicators calculated for NKE
2025-06-21 12:52:22,718 - finmas_enhanced - ERROR - Risk analysis failed for NKE: Invalid format specifier '.2f if stock_data.beta else 'N/A'' for object of type 'float'
2025-06-21 12:52:22,718 - finmas_enhanced - ERROR - Risk analysis failed for NKE: Invalid format specifier '.2f if stock_data.beta else 'N/A'' for object of type 'float'
2025-06-21 12:52:22,718 - finmas_enhanced - INFO - Investment Advisor analyzing NKE
2025-06-21 12:52:22,718 - finmas_enhanced - INFO - Investment Advisor analyzing NKE
2025-06-21 12:52:22,718 - finmas_enhanced - INFO - Fetching stock data for NKE
2025-06-21 12:52:22,718 - finmas_enhanced - INFO - Fetching stock data for NKE
2025-06-21 12:52:22,856 - finmas_enhanced - INFO - Stock data retrieved for NKE: $59.79
2025-06-21 12:52:22,856 - finmas_enhanced - INFO - Stock data retrieved for NKE: $59.79
2025-06-21 12:52:22,856 - finmas_enhanced - ERROR - Investment advisory failed for NKE: Invalid format specifier '.1f if stock_data.pe_ratio else 'N/A'' for object of type 'float'
2025-06-21 12:52:22,856 - finmas_enhanced - ERROR - Investment advisory failed for NKE: Invalid format specifier '.1f if stock_data.pe_ratio else 'N/A'' for object of type 'float'
2025-06-21 12:52:44,851 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1709+816=2525, Cost: $0.0174
2025-06-21 12:52:44,851 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1709+816=2525, Cost: $0.0174
2025-06-21 12:52:44,851 - finmas_enhanced - INFO - Synthesis completed for NKE - Total cost: $0.0414
2025-06-21 12:52:44,851 - finmas_enhanced - INFO - Synthesis completed for NKE - Total cost: $0.0414
2025-06-21 12:56:46,235 - finmas_enhanced - INFO - Analyzing query: and puma...
2025-06-21 12:56:46,235 - finmas_enhanced - INFO - Analyzing query: and puma...
2025-06-21 12:56:46,235 - finmas_enhanced - INFO - Analyzing query intent: and puma...
2025-06-21 12:56:46,235 - finmas_enhanced - INFO - Analyzing query intent: and puma...
2025-06-21 12:56:48,127 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 643+83=726, Cost: $0.0032
2025-06-21 12:56:48,127 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 643+83=726, Cost: $0.0032
2025-06-21 12:56:48,127 - finmas_enhanced - INFO - Intent analysis complete: analyze, tickers: ['PUMA.DE'], confidence: 0.7
2025-06-21 12:56:48,127 - finmas_enhanced - INFO - Intent analysis complete: analyze, tickers: ['PUMA.DE'], confidence: 0.7
2025-06-21 12:56:48,127 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['PUMA.DE'], confidence: 0.7
2025-06-21 12:56:48,127 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['PUMA.DE'], confidence: 0.7
2025-06-21 12:56:48,741 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 164+10=174, Cost: $0.0006
2025-06-21 12:56:48,741 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 164+10=174, Cost: $0.0006
2025-06-21 12:56:48,748 - finmas_enhanced - INFO - Starting collaborative analysis for PUMA.DE with agents: ['technical', 'fundamental', 'news', 'market', 'risk', 'investment']
2025-06-21 12:56:48,748 - finmas_enhanced - INFO - Starting collaborative analysis for PUMA.DE with agents: ['technical', 'fundamental', 'news', 'market', 'risk', 'investment']
2025-06-21 12:56:48,748 - finmas_enhanced - INFO - Technical Analyst analyzing PUMA.DE
2025-06-21 12:56:48,748 - finmas_enhanced - INFO - Technical Analyst analyzing PUMA.DE
2025-06-21 12:56:48,748 - finmas_enhanced - INFO - Fetching stock data for PUMA.DE
2025-06-21 12:56:48,748 - finmas_enhanced - INFO - Fetching stock data for PUMA.DE
2025-06-21 12:56:49,606 - finmas_enhanced - WARNING - No data found for ticker PUMA.DE
2025-06-21 12:56:49,606 - finmas_enhanced - WARNING - No data found for ticker PUMA.DE
2025-06-21 12:56:49,606 - finmas_enhanced - INFO - Calculating technical indicators for PUMA.DE
2025-06-21 12:56:49,606 - finmas_enhanced - INFO - Calculating technical indicators for PUMA.DE
2025-06-21 12:56:50,741 - finmas_enhanced - ERROR - Technical analysis failed for PUMA.DE: Unable to get technical data for PUMA.DE
2025-06-21 12:56:50,741 - finmas_enhanced - ERROR - Technical analysis failed for PUMA.DE: Unable to get technical data for PUMA.DE
2025-06-21 12:56:50,741 - finmas_enhanced - INFO - Fundamental Analyst analyzing PUMA.DE
2025-06-21 12:56:50,741 - finmas_enhanced - INFO - Fundamental Analyst analyzing PUMA.DE
2025-06-21 12:56:50,741 - finmas_enhanced - INFO - Fetching stock data for PUMA.DE
2025-06-21 12:56:50,741 - finmas_enhanced - INFO - Fetching stock data for PUMA.DE
2025-06-21 12:56:51,198 - finmas_enhanced - WARNING - No data found for ticker PUMA.DE
2025-06-21 12:56:51,198 - finmas_enhanced - WARNING - No data found for ticker PUMA.DE
2025-06-21 12:56:51,198 - finmas_enhanced - INFO - Fetching financial metrics for PUMA.DE
2025-06-21 12:56:51,198 - finmas_enhanced - INFO - Fetching financial metrics for PUMA.DE
2025-06-21 12:56:52,115 - finmas_enhanced - INFO - Financial metrics retrieved for PUMA.DE
2025-06-21 12:56:52,115 - finmas_enhanced - INFO - Financial metrics retrieved for PUMA.DE
2025-06-21 12:56:52,118 - finmas_enhanced - ERROR - Fundamental analysis failed for PUMA.DE: Unable to get fundamental data for PUMA.DE
2025-06-21 12:56:52,118 - finmas_enhanced - ERROR - Fundamental analysis failed for PUMA.DE: Unable to get fundamental data for PUMA.DE
2025-06-21 12:56:52,118 - finmas_enhanced - INFO - News Analyst analyzing PUMA.DE
2025-06-21 12:56:52,118 - finmas_enhanced - INFO - News Analyst analyzing PUMA.DE
2025-06-21 12:56:52,118 - finmas_enhanced - INFO - Fetching news for PUMA.DE
2025-06-21 12:56:52,118 - finmas_enhanced - INFO - Fetching news for PUMA.DE
2025-06-21 12:56:52,492 - finmas_enhanced - INFO - Retrieved 0 news items for PUMA.DE
2025-06-21 12:56:52,492 - finmas_enhanced - INFO - Retrieved 0 news items for PUMA.DE
2025-06-21 12:56:52,492 - finmas_enhanced - INFO - Fetching stock data for PUMA.DE
2025-06-21 12:56:52,492 - finmas_enhanced - INFO - Fetching stock data for PUMA.DE
2025-06-21 12:56:52,912 - finmas_enhanced - WARNING - No data found for ticker PUMA.DE
2025-06-21 12:56:52,912 - finmas_enhanced - WARNING - No data found for ticker PUMA.DE
2025-06-21 12:56:52,912 - finmas_enhanced - ERROR - News analysis failed for PUMA.DE: Unable to get news data for PUMA.DE
2025-06-21 12:56:52,912 - finmas_enhanced - ERROR - News analysis failed for PUMA.DE: Unable to get news data for PUMA.DE
2025-06-21 12:56:52,912 - finmas_enhanced - INFO - Market Analyst analyzing PUMA.DE
2025-06-21 12:56:52,912 - finmas_enhanced - INFO - Market Analyst analyzing PUMA.DE
2025-06-21 12:56:52,912 - finmas_enhanced - INFO - Fetching stock data for PUMA.DE
2025-06-21 12:56:52,912 - finmas_enhanced - INFO - Fetching stock data for PUMA.DE
2025-06-21 12:56:53,310 - finmas_enhanced - WARNING - No data found for ticker PUMA.DE
2025-06-21 12:56:53,310 - finmas_enhanced - WARNING - No data found for ticker PUMA.DE
2025-06-21 12:56:53,310 - finmas_enhanced - ERROR - Market analysis failed for PUMA.DE: Unable to get stock data for PUMA.DE
2025-06-21 12:56:53,310 - finmas_enhanced - ERROR - Market analysis failed for PUMA.DE: Unable to get stock data for PUMA.DE
2025-06-21 12:56:53,312 - finmas_enhanced - INFO - Risk Analyst analyzing PUMA.DE
2025-06-21 12:56:53,312 - finmas_enhanced - INFO - Risk Analyst analyzing PUMA.DE
2025-06-21 12:56:53,312 - finmas_enhanced - INFO - Fetching stock data for PUMA.DE
2025-06-21 12:56:53,312 - finmas_enhanced - INFO - Fetching stock data for PUMA.DE
2025-06-21 12:56:53,699 - finmas_enhanced - WARNING - No data found for ticker PUMA.DE
2025-06-21 12:56:53,699 - finmas_enhanced - WARNING - No data found for ticker PUMA.DE
2025-06-21 12:56:53,699 - finmas_enhanced - INFO - Fetching financial metrics for PUMA.DE
2025-06-21 12:56:53,699 - finmas_enhanced - INFO - Fetching financial metrics for PUMA.DE
2025-06-21 12:56:54,125 - finmas_enhanced - INFO - Financial metrics retrieved for PUMA.DE
2025-06-21 12:56:54,125 - finmas_enhanced - INFO - Financial metrics retrieved for PUMA.DE
2025-06-21 12:56:54,125 - finmas_enhanced - INFO - Calculating technical indicators for PUMA.DE
2025-06-21 12:56:54,125 - finmas_enhanced - INFO - Calculating technical indicators for PUMA.DE
2025-06-21 12:56:54,853 - finmas_enhanced - ERROR - Risk analysis failed for PUMA.DE: Unable to get stock data for PUMA.DE
2025-06-21 12:56:54,853 - finmas_enhanced - ERROR - Risk analysis failed for PUMA.DE: Unable to get stock data for PUMA.DE
2025-06-21 12:56:54,854 - finmas_enhanced - INFO - Investment Advisor analyzing PUMA.DE
2025-06-21 12:56:54,854 - finmas_enhanced - INFO - Investment Advisor analyzing PUMA.DE
2025-06-21 12:56:54,854 - finmas_enhanced - INFO - Fetching stock data for PUMA.DE
2025-06-21 12:56:54,854 - finmas_enhanced - INFO - Fetching stock data for PUMA.DE
2025-06-21 12:56:55,278 - finmas_enhanced - WARNING - No data found for ticker PUMA.DE
2025-06-21 12:56:55,278 - finmas_enhanced - WARNING - No data found for ticker PUMA.DE
2025-06-21 12:56:55,285 - finmas_enhanced - ERROR - Investment advisory failed for PUMA.DE: Unable to get stock data for PUMA.DE
2025-06-21 12:56:55,285 - finmas_enhanced - ERROR - Investment advisory failed for PUMA.DE: Unable to get stock data for PUMA.DE
2025-06-21 12:57:06,076 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 292+367=659, Cost: $0.0064
2025-06-21 12:57:06,076 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 292+367=659, Cost: $0.0064
2025-06-21 12:57:06,076 - finmas_enhanced - INFO - Synthesis completed for PUMA.DE - Total cost: $0.0064
2025-06-21 12:57:06,076 - finmas_enhanced - INFO - Synthesis completed for PUMA.DE - Total cost: $0.0064
2025-06-21 12:58:40,135 - finmas_enhanced - INFO - Analyzing query: puma perforamce last year...
2025-06-21 12:58:40,135 - finmas_enhanced - INFO - Analyzing query: puma perforamce last year...
2025-06-21 12:58:40,135 - finmas_enhanced - INFO - Analyzing query intent: puma perforamce last year...
2025-06-21 12:58:40,135 - finmas_enhanced - INFO - Analyzing query intent: puma perforamce last year...
2025-06-21 12:58:42,777 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 648+93=741, Cost: $0.0033
2025-06-21 12:58:42,777 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 648+93=741, Cost: $0.0033
2025-06-21 12:58:42,777 - finmas_enhanced - INFO - Intent analysis complete: analyze, tickers: ['PUMSY'], confidence: 0.9
2025-06-21 12:58:42,777 - finmas_enhanced - INFO - Intent analysis complete: analyze, tickers: ['PUMSY'], confidence: 0.9
2025-06-21 12:58:42,777 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['PUMSY'], confidence: 0.9
2025-06-21 12:58:42,777 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['PUMSY'], confidence: 0.9
2025-06-21 12:58:44,370 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 162+4=166, Cost: $0.0005
2025-06-21 12:58:44,370 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 162+4=166, Cost: $0.0005
2025-06-21 12:59:00,784 - finmas_enhanced - INFO - Analyzing query: pum perforamce last year...
2025-06-21 12:59:00,784 - finmas_enhanced - INFO - Analyzing query: pum perforamce last year...
2025-06-21 12:59:00,790 - finmas_enhanced - INFO - Analyzing query intent: pum perforamce last year...
2025-06-21 12:59:00,790 - finmas_enhanced - INFO - Analyzing query intent: pum perforamce last year...
2025-06-21 12:59:03,875 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 648+94=742, Cost: $0.0034
2025-06-21 12:59:03,875 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 648+94=742, Cost: $0.0034
2025-06-21 12:59:03,876 - finmas_enhanced - INFO - Intent analysis complete: analyze, tickers: ['PUM.DE'], confidence: 0.8
2025-06-21 12:59:03,876 - finmas_enhanced - INFO - Intent analysis complete: analyze, tickers: ['PUM.DE'], confidence: 0.8
2025-06-21 12:59:03,876 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['PUM.DE'], confidence: 0.8
2025-06-21 12:59:03,876 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['PUM.DE'], confidence: 0.8
2025-06-21 12:59:04,580 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 163+9=172, Cost: $0.0006
2025-06-21 12:59:04,580 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 163+9=172, Cost: $0.0006
2025-06-21 12:59:04,587 - finmas_enhanced - INFO - Starting collaborative analysis for PUM.DE with agents: ['technical', 'fundamental', 'news', 'market', 'risk', 'investment']
2025-06-21 12:59:04,587 - finmas_enhanced - INFO - Starting collaborative analysis for PUM.DE with agents: ['technical', 'fundamental', 'news', 'market', 'risk', 'investment']
2025-06-21 12:59:04,588 - finmas_enhanced - INFO - Technical Analyst analyzing PUM.DE
2025-06-21 12:59:04,588 - finmas_enhanced - INFO - Technical Analyst analyzing PUM.DE
2025-06-21 12:59:04,588 - finmas_enhanced - INFO - Fetching stock data for PUM.DE
2025-06-21 12:59:04,588 - finmas_enhanced - INFO - Fetching stock data for PUM.DE
2025-06-21 12:59:05,205 - finmas_enhanced - INFO - Stock data retrieved for PUM.DE: $21.41
2025-06-21 12:59:05,205 - finmas_enhanced - INFO - Stock data retrieved for PUM.DE: $21.41
2025-06-21 12:59:05,205 - finmas_enhanced - INFO - Calculating technical indicators for PUM.DE
2025-06-21 12:59:05,205 - finmas_enhanced - INFO - Calculating technical indicators for PUM.DE
2025-06-21 12:59:05,528 - finmas_enhanced - INFO - Technical indicators calculated for PUM.DE
2025-06-21 12:59:05,528 - finmas_enhanced - INFO - Technical indicators calculated for PUM.DE
2025-06-21 12:59:05,528 - finmas_enhanced - INFO - Fundamental Analyst analyzing PUM.DE
2025-06-21 12:59:05,528 - finmas_enhanced - INFO - Fundamental Analyst analyzing PUM.DE
2025-06-21 12:59:05,530 - finmas_enhanced - INFO - Fetching stock data for PUM.DE
2025-06-21 12:59:05,530 - finmas_enhanced - INFO - Fetching stock data for PUM.DE
2025-06-21 12:59:05,665 - finmas_enhanced - INFO - Stock data retrieved for PUM.DE: $21.41
2025-06-21 12:59:05,665 - finmas_enhanced - INFO - Stock data retrieved for PUM.DE: $21.41
2025-06-21 12:59:05,665 - finmas_enhanced - INFO - Fetching financial metrics for PUM.DE
2025-06-21 12:59:05,665 - finmas_enhanced - INFO - Fetching financial metrics for PUM.DE
2025-06-21 12:59:06,556 - finmas_enhanced - INFO - Financial metrics retrieved for PUM.DE
2025-06-21 12:59:06,556 - finmas_enhanced - INFO - Financial metrics retrieved for PUM.DE
2025-06-21 12:59:06,556 - finmas_enhanced - ERROR - Fundamental analysis failed for PUM.DE: Invalid format specifier '.1f if stock_data.pe_ratio else 'N/A'' for object of type 'float'
2025-06-21 12:59:06,556 - finmas_enhanced - ERROR - Fundamental analysis failed for PUM.DE: Invalid format specifier '.1f if stock_data.pe_ratio else 'N/A'' for object of type 'float'
2025-06-21 12:59:06,556 - finmas_enhanced - INFO - News Analyst analyzing PUM.DE
2025-06-21 12:59:06,556 - finmas_enhanced - INFO - News Analyst analyzing PUM.DE
2025-06-21 12:59:06,558 - finmas_enhanced - INFO - Fetching news for PUM.DE
2025-06-21 12:59:06,558 - finmas_enhanced - INFO - Fetching news for PUM.DE
2025-06-21 12:59:06,976 - finmas_enhanced - INFO - Retrieved 10 news items for PUM.DE
2025-06-21 12:59:06,976 - finmas_enhanced - INFO - Retrieved 10 news items for PUM.DE
2025-06-21 12:59:06,976 - finmas_enhanced - INFO - Fetching stock data for PUM.DE
2025-06-21 12:59:06,976 - finmas_enhanced - INFO - Fetching stock data for PUM.DE
2025-06-21 12:59:07,201 - finmas_enhanced - INFO - Stock data retrieved for PUM.DE: $21.41
2025-06-21 12:59:07,201 - finmas_enhanced - INFO - Stock data retrieved for PUM.DE: $21.41
2025-06-21 12:59:07,201 - finmas_enhanced - INFO - Market Analyst analyzing PUM.DE
2025-06-21 12:59:07,201 - finmas_enhanced - INFO - Market Analyst analyzing PUM.DE
2025-06-21 12:59:07,203 - finmas_enhanced - INFO - Fetching stock data for PUM.DE
2025-06-21 12:59:07,203 - finmas_enhanced - INFO - Fetching stock data for PUM.DE
2025-06-21 12:59:07,345 - finmas_enhanced - INFO - Stock data retrieved for PUM.DE: $21.41
2025-06-21 12:59:07,345 - finmas_enhanced - INFO - Stock data retrieved for PUM.DE: $21.41
2025-06-21 12:59:07,345 - finmas_enhanced - ERROR - Market analysis failed for PUM.DE: Invalid format specifier '.2f if stock_data.beta else 'N/A'' for object of type 'float'
2025-06-21 12:59:07,345 - finmas_enhanced - ERROR - Market analysis failed for PUM.DE: Invalid format specifier '.2f if stock_data.beta else 'N/A'' for object of type 'float'
2025-06-21 12:59:22,080 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 491+419=910, Cost: $0.0078
2025-06-21 12:59:22,080 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 491+419=910, Cost: $0.0078
2025-06-21 12:59:23,371 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 590+743=1333, Cost: $0.0129
2025-06-21 12:59:23,371 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 590+743=1333, Cost: $0.0129
2025-06-21 12:59:23,371 - finmas_enhanced - INFO - Risk Analyst analyzing PUM.DE
2025-06-21 12:59:23,371 - finmas_enhanced - INFO - Risk Analyst analyzing PUM.DE
2025-06-21 12:59:23,371 - finmas_enhanced - INFO - Fetching stock data for PUM.DE
2025-06-21 12:59:23,371 - finmas_enhanced - INFO - Fetching stock data for PUM.DE
2025-06-21 12:59:23,604 - finmas_enhanced - INFO - Stock data retrieved for PUM.DE: $21.41
2025-06-21 12:59:23,604 - finmas_enhanced - INFO - Stock data retrieved for PUM.DE: $21.41
2025-06-21 12:59:23,605 - finmas_enhanced - INFO - Fetching financial metrics for PUM.DE
2025-06-21 12:59:23,605 - finmas_enhanced - INFO - Fetching financial metrics for PUM.DE
2025-06-21 12:59:23,801 - finmas_enhanced - INFO - Financial metrics retrieved for PUM.DE
2025-06-21 12:59:23,801 - finmas_enhanced - INFO - Financial metrics retrieved for PUM.DE
2025-06-21 12:59:23,802 - finmas_enhanced - INFO - Calculating technical indicators for PUM.DE
2025-06-21 12:59:23,802 - finmas_enhanced - INFO - Calculating technical indicators for PUM.DE
2025-06-21 12:59:23,957 - finmas_enhanced - INFO - Technical indicators calculated for PUM.DE
2025-06-21 12:59:23,957 - finmas_enhanced - INFO - Technical indicators calculated for PUM.DE
2025-06-21 12:59:23,957 - finmas_enhanced - ERROR - Risk analysis failed for PUM.DE: Invalid format specifier '.2f if stock_data.beta else 'N/A'' for object of type 'float'
2025-06-21 12:59:23,957 - finmas_enhanced - ERROR - Risk analysis failed for PUM.DE: Invalid format specifier '.2f if stock_data.beta else 'N/A'' for object of type 'float'
2025-06-21 12:59:23,957 - finmas_enhanced - INFO - Investment Advisor analyzing PUM.DE
2025-06-21 12:59:23,957 - finmas_enhanced - INFO - Investment Advisor analyzing PUM.DE
2025-06-21 12:59:23,960 - finmas_enhanced - INFO - Fetching stock data for PUM.DE
2025-06-21 12:59:23,960 - finmas_enhanced - INFO - Fetching stock data for PUM.DE
2025-06-21 12:59:24,093 - finmas_enhanced - INFO - Stock data retrieved for PUM.DE: $21.41
2025-06-21 12:59:24,093 - finmas_enhanced - INFO - Stock data retrieved for PUM.DE: $21.41
2025-06-21 12:59:24,095 - finmas_enhanced - ERROR - Investment advisory failed for PUM.DE: Invalid format specifier '.1f if stock_data.pe_ratio else 'N/A'' for object of type 'float'
2025-06-21 12:59:24,095 - finmas_enhanced - ERROR - Investment advisory failed for PUM.DE: Invalid format specifier '.1f if stock_data.pe_ratio else 'N/A'' for object of type 'float'
2025-06-21 12:59:50,399 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1489+818=2307, Cost: $0.0167
2025-06-21 12:59:50,399 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1489+818=2307, Cost: $0.0167
2025-06-21 12:59:50,399 - finmas_enhanced - INFO - Synthesis completed for PUM.DE - Total cost: $0.0374
2025-06-21 12:59:50,399 - finmas_enhanced - INFO - Synthesis completed for PUM.DE - Total cost: $0.0374
2025-06-21 13:00:52,468 - finmas_enhanced - INFO - Analyzing query: Have there been share repurchase programs launched or terminated? from 8-K form...
2025-06-21 13:00:52,468 - finmas_enhanced - INFO - Analyzing query: Have there been share repurchase programs launched or terminated? from 8-K form...
2025-06-21 13:00:52,468 - finmas_enhanced - INFO - Analyzing query intent: Have there been share repurchase programs launched or terminated? from 8-K form...
2025-06-21 13:00:52,468 - finmas_enhanced - INFO - Analyzing query intent: Have there been share repurchase programs launched or terminated? from 8-K form...
2025-06-21 13:00:55,225 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 658+90=748, Cost: $0.0033
2025-06-21 13:00:55,225 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 658+90=748, Cost: $0.0033
2025-06-21 13:00:55,226 - finmas_enhanced - INFO - Intent analysis complete: fundamental, tickers: [], confidence: 0.9
2025-06-21 13:00:55,226 - finmas_enhanced - INFO - Intent analysis complete: fundamental, tickers: [], confidence: 0.9
2025-06-21 13:00:55,226 - finmas_enhanced - INFO - Intent analysis: fundamental, tickers: [], confidence: 0.9
2025-06-21 13:00:55,226 - finmas_enhanced - INFO - Intent analysis: fundamental, tickers: [], confidence: 0.9
2025-06-21 13:01:45,484 - finmas_enhanced - INFO - Cleaning up Financial Analyst resources
2025-06-21 13:01:45,484 - finmas_enhanced - INFO - Cleaning up Financial Analyst resources
