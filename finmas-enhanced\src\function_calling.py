"""
Function Calling Orchestrator for AWS Bedrock

This module implements native function calling capabilities
for the financial analysis system.
"""

import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple

from .bedrock_client import BedrockClient
from .data_sources import FinancialDataProvider
from utils.logging_utils import get_logger


class FunctionCallOrchestrator:
    """Orchestrates function calling with AWS Bedrock"""
    
    def __init__(self, bedrock_client: BedrockClient, data_provider: FinancialDataProvider):
        self.bedrock_client = bedrock_client
        self.data_provider = data_provider
        self.logger = get_logger()
        
        # Define available functions for Bedrock
        self.function_tools = [
            {
                "toolSpec": {
                    "name": "get_stock_data",
                    "description": "Get real-time stock price and basic information",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol (e.g., AAPL, MSFT)"
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "get_financial_metrics",
                    "description": "Get comprehensive financial metrics and ratios",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol"
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "get_technical_indicators",
                    "description": "Calculate technical analysis indicators",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol"
                                },
                                "period": {
                                    "type": "string",
                                    "description": "Time period for analysis (1y, 6mo, 3mo)",
                                    "default": "1y"
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "get_news",
                    "description": "Get recent news articles for a stock",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol"
                                },
                                "days_back": {
                                    "type": "integer",
                                    "description": "Number of days to look back for news",
                                    "default": 7
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "validate_ticker",
                    "description": "Validate if a ticker symbol exists and is tradeable",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol to validate"
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "get_sec_filing_data",
                    "description": "Get comprehensive SEC filing data and analysis with temporal specificity",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol"
                                },
                                "form_type": {
                                    "type": "string",
                                    "description": "SEC form type (10-K, 10-Q, 8-K, DEF 14A)",
                                    "default": "10-K"
                                },
                                "filing_date": {
                                    "type": "string",
                                    "description": "Specific filing date (YYYY-MM-DD) or period (2024-Q3, 2023-Q4, 2024-annual)"
                                },
                                "date_range": {
                                    "type": "object",
                                    "properties": {
                                        "start_date": {"type": "string", "description": "Start date (YYYY-MM-DD)"},
                                        "end_date": {"type": "string", "description": "End date (YYYY-MM-DD)"}
                                    },
                                    "description": "Date range for comparative analysis"
                                },
                                "count": {
                                    "type": "integer",
                                    "description": "Number of recent filings to analyze",
                                    "default": 4
                                },
                                "full_document": {
                                    "type": "boolean",
                                    "description": "Whether to retrieve complete document content vs excerpts",
                                    "default": False
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "get_temporal_financial_data",
                    "description": "Get financial data for specific time periods with quarter/year specificity",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol"
                                },
                                "period_type": {
                                    "type": "string",
                                    "enum": ["quarterly", "annual", "ttm"],
                                    "description": "Type of reporting period",
                                    "default": "quarterly"
                                },
                                "specific_period": {
                                    "type": "string",
                                    "description": "Specific period (e.g., '2024-Q3', '2023-Q4', '2024', '2023')"
                                },
                                "comparison_periods": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "Additional periods for comparison (e.g., ['2023-Q3', '2022-Q3'])"
                                },
                                "metrics": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "Specific metrics to retrieve (revenue, capex, operating_income, etc.)",
                                    "default": ["revenue", "net_income", "operating_income", "capex"]
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "get_complete_sec_document",
                    "description": "Retrieve complete SEC document with full content and structured analysis",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol"
                                },
                                "form_type": {
                                    "type": "string",
                                    "description": "SEC form type (10-K, 10-Q, 8-K, DEF 14A)",
                                    "default": "10-K"
                                },
                                "filing_date": {
                                    "type": "string",
                                    "description": "Specific filing date (YYYY-MM-DD) or 'latest'"
                                },
                                "sections": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "Specific sections to extract (business, risk_factors, mda, financial_statements, notes)",
                                    "default": ["business", "risk_factors", "mda", "financial_statements"]
                                },
                                "include_exhibits": {
                                    "type": "boolean",
                                    "description": "Whether to include exhibit analysis",
                                    "default": False
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "analyze_financial_performance",
                    "description": "Perform comprehensive financial performance analysis",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol"
                                },
                                "analysis_type": {
                                    "type": "string",
                                    "description": "Type of analysis (revenue_growth, margin_trends, eps_analysis, cash_flow)",
                                    "default": "comprehensive"
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "analyze_operational_metrics",
                    "description": "Analyze operational metrics and segment performance",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol"
                                },
                                "focus_area": {
                                    "type": "string",
                                    "description": "Focus area (segments, geography, unit_sales, customer_metrics)",
                                    "default": "comprehensive"
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "analyze_risk_factors",
                    "description": "Perform comprehensive risk monitoring and red flag analysis",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol"
                                },
                                "risk_category": {
                                    "type": "string",
                                    "description": "Risk category (going_concern, material_weakness, litigation, executive_changes)",
                                    "default": "comprehensive"
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "analyze_valuation_metrics",
                    "description": "Perform valuation and financial health analysis",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol"
                                },
                                "valuation_method": {
                                    "type": "string",
                                    "description": "Valuation method (book_value, insider_activity, dividend_analysis, roic_roe)",
                                    "default": "comprehensive"
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "analyze_strategic_position",
                    "description": "Perform strategic analysis and market positioning assessment",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol"
                                },
                                "strategic_focus": {
                                    "type": "string",
                                    "description": "Strategic focus (growth_drivers, competitive_position, ma_activity, regulatory_risks)",
                                    "default": "comprehensive"
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "compare_companies",
                    "description": "Perform comparative analysis across multiple companies",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "tickers": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "List of stock ticker symbols to compare"
                                },
                                "comparison_metrics": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "Metrics to compare (revenue_growth, margins, valuation, risk)",
                                    "default": ["revenue_growth", "margins", "valuation"]
                                }
                            },
                            "required": ["tickers"]
                        }
                    }
                }
            }
        ]
    
    async def execute_with_functions(
        self,
        messages: List[Dict[str, Any]],
        system_message: Optional[str] = None,
        max_iterations: int = 3,
        query_intent: Optional[Any] = None
    ) -> Tuple[str, List[Dict[str, Any]], Dict[str, Any]]:
        """
        Execute conversation with function calling support
        
        Args:
            messages: Conversation messages
            system_message: Optional system message
            max_iterations: Maximum function calling iterations
            
        Returns:
            Tuple of (final_response, function_calls_made, cost_info)
        """
        total_cost = 0.0
        all_function_calls = []
        current_messages = messages.copy()

        # Enhance system message based on temporal specificity
        enhanced_system_message = self._enhance_system_message_for_temporal(system_message, query_intent)

        for iteration in range(max_iterations):
            self.logger.logger.info(f"Function calling iteration {iteration + 1}")
            
            # Call Bedrock with function tools
            response_text, function_calls, cost_info = await self.bedrock_client.converse_with_functions(
                messages=current_messages,
                tools=self.function_tools,
                system_message=enhanced_system_message
            )
            
            total_cost += cost_info['total_cost']
            
            # If no function calls, we're done
            if not function_calls:
                final_cost_info = cost_info.copy()
                final_cost_info['total_cost'] = total_cost
                final_cost_info['function_calls'] = len(all_function_calls)
                return response_text, all_function_calls, final_cost_info
            
            # Execute function calls
            function_results = await self._execute_functions(function_calls)
            all_function_calls.extend(function_calls)
            
            # Add assistant message with function calls
            assistant_message = {
                "role": "assistant",
                "content": []
            }
            
            if response_text.strip():
                assistant_message["content"].append({"text": response_text})
            
            for func_call in function_calls:
                assistant_message["content"].append({
                    "toolUse": {
                        "toolUseId": func_call["toolUseId"],
                        "name": func_call["name"],
                        "input": func_call["input"]
                    }
                })
            
            current_messages.append(assistant_message)
            
            # Add function results as user messages
            for result in function_results:
                result_message = {
                    "role": "user",
                    "content": [{
                        "toolResult": {
                            "toolUseId": result["toolUseId"],
                            "content": result["content"]
                        }
                    }]
                }
                current_messages.append(result_message)
        
        # Final call without functions to get summary
        final_response, final_cost_info = await self.bedrock_client.converse_async(
            messages=current_messages,
            system_message=enhanced_system_message
        )
        
        total_cost += final_cost_info['total_cost']
        
        # Extract final response text
        final_text = ""
        output_message = final_response.get('output', {}).get('message', {})
        content = output_message.get('content', [])
        
        for content_block in content:
            if 'text' in content_block:
                final_text += content_block['text']
        
        final_cost_info['total_cost'] = total_cost
        final_cost_info['function_calls'] = len(all_function_calls)
        
        return final_text, all_function_calls, final_cost_info
    
    async def _execute_functions(self, function_calls: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Execute function calls and return results"""
        results = []
        
        async with self.data_provider:
            for func_call in function_calls:
                function_name = func_call['name']
                parameters = func_call['input']
                tool_use_id = func_call['toolUseId']
                
                self.logger.logger.info(f"Executing function: {function_name} with params: {parameters}")
                
                try:
                    # Execute the appropriate function
                    if function_name == "get_stock_data":
                        result = await self._get_stock_data(parameters)
                    elif function_name == "get_financial_metrics":
                        result = await self._get_financial_metrics(parameters)
                    elif function_name == "get_technical_indicators":
                        result = await self._get_technical_indicators(parameters)
                    elif function_name == "get_news":
                        result = await self._get_news(parameters)
                    elif function_name == "validate_ticker":
                        result = await self._validate_ticker(parameters)
                    elif function_name == "get_sec_filing_data":
                        result = await self._get_sec_filing_data(parameters)
                    elif function_name == "get_temporal_financial_data":
                        result = await self._get_temporal_financial_data(parameters)
                    elif function_name == "get_complete_sec_document":
                        result = await self._get_complete_sec_document(parameters)
                    elif function_name == "analyze_financial_performance":
                        result = await self._analyze_financial_performance(parameters)
                    elif function_name == "analyze_operational_metrics":
                        result = await self._analyze_operational_metrics(parameters)
                    elif function_name == "analyze_risk_factors":
                        result = await self._analyze_risk_factors(parameters)
                    elif function_name == "analyze_valuation_metrics":
                        result = await self._analyze_valuation_metrics(parameters)
                    elif function_name == "analyze_strategic_position":
                        result = await self._analyze_strategic_position(parameters)
                    elif function_name == "compare_companies":
                        result = await self._compare_companies(parameters)
                    else:
                        result = f"Unknown function: {function_name}"
                    
                    results.append({
                        "toolUseId": tool_use_id,
                        "content": [{"text": result}]
                    })
                    
                except Exception as e:
                    error_message = f"Error executing {function_name}: {str(e)}"
                    self.logger.logger.error(error_message)
                    
                    results.append({
                        "toolUseId": tool_use_id,
                        "content": [{"text": error_message}]
                    })
        
        return results
    
    async def _get_stock_data(self, parameters: Dict[str, Any]) -> str:
        """Get stock data function implementation"""
        ticker = parameters['ticker']
        stock_data = await self.data_provider.get_stock_data(ticker)
        
        if not stock_data:
            return f"No stock data found for {ticker}"
        
        return json.dumps({
            "ticker": stock_data.ticker,
            "current_price": stock_data.current_price,
            "change": stock_data.change,
            "change_percent": stock_data.change_percent,
            "volume": stock_data.volume,
            "market_cap": stock_data.market_cap,
            "pe_ratio": stock_data.pe_ratio,
            "52_week_high": stock_data.fifty_two_week_high,
            "52_week_low": stock_data.fifty_two_week_low,
            "timestamp": stock_data.timestamp
        }, indent=2)
    
    async def _get_financial_metrics(self, parameters: Dict[str, Any]) -> str:
        """Get financial metrics function implementation"""
        ticker = parameters['ticker']
        metrics = await self.data_provider.get_financial_metrics(ticker)
        
        if not metrics:
            return f"No financial metrics found for {ticker}"
        
        return json.dumps({
            "ticker": metrics.ticker,
            "revenue_ttm": metrics.revenue_ttm,
            "revenue_growth": metrics.revenue_growth,
            "net_income": metrics.net_income,
            "gross_margin": metrics.gross_margin,
            "operating_margin": metrics.operating_margin,
            "profit_margin": metrics.profit_margin,
            "roe": metrics.roe,
            "roa": metrics.roa,
            "debt_to_equity": metrics.debt_to_equity,
            "current_ratio": metrics.current_ratio,
            "free_cash_flow": metrics.free_cash_flow,
            "earnings_per_share": metrics.earnings_per_share,
            "timestamp": metrics.timestamp
        }, indent=2)
    
    async def _get_technical_indicators(self, parameters: Dict[str, Any]) -> str:
        """Get technical indicators function implementation"""
        ticker = parameters['ticker']
        period = parameters.get('period', '1y')
        
        indicators = await self.data_provider.get_technical_indicators(ticker, period)
        
        if not indicators:
            return f"No technical indicators found for {ticker}"
        
        return json.dumps({
            "ticker": indicators.ticker,
            "rsi": indicators.rsi,
            "macd": indicators.macd,
            "moving_averages": indicators.moving_averages,
            "bollinger_bands": indicators.bollinger_bands,
            "support_levels": indicators.support_levels,
            "resistance_levels": indicators.resistance_levels,
            "trend": indicators.trend,
            "momentum": indicators.momentum,
            "timestamp": indicators.timestamp
        }, indent=2)
    
    async def _get_news(self, parameters: Dict[str, Any]) -> str:
        """Get news function implementation"""
        ticker = parameters['ticker']
        days_back = parameters.get('days_back', 7)
        
        news_items = await self.data_provider.get_news(ticker, days_back)
        
        if not news_items:
            return f"No recent news found for {ticker}"
        
        news_data = []
        for item in news_items:
            news_data.append({
                "title": item.title,
                "summary": item.summary,
                "source": item.source,
                "published_date": item.published_date,
                "url": item.url
            })
        
        return json.dumps({
            "ticker": ticker,
            "news_count": len(news_items),
            "news_items": news_data
        }, indent=2)
    
    async def _validate_ticker(self, parameters: Dict[str, Any]) -> str:
        """Validate ticker function implementation"""
        ticker = parameters['ticker']
        is_valid = await self.data_provider.validate_ticker(ticker)
        
        return json.dumps({
            "ticker": ticker,
            "is_valid": is_valid,
            "message": f"{ticker} is {'valid' if is_valid else 'invalid'}"
        })

    async def _get_sec_filing_data(self, parameters: Dict[str, Any]) -> str:
        """Get SEC filing data function implementation with enhanced temporal support"""
        from .sec_data_provider import EnhancedSECDataProvider
        from config import Config

        ticker = parameters['ticker']
        form_type = parameters.get('form_type', '10-K')
        filing_date = parameters.get('filing_date')
        date_range = parameters.get('date_range')
        count = parameters.get('count', 4)
        full_document = parameters.get('full_document', False)

        try:
            config = Config()
            sec_provider = EnhancedSECDataProvider(config)

            # Handle different retrieval modes
            if filing_date and filing_date != 'latest':
                # Get specific period filing
                if 'Q' in filing_date or len(filing_date) == 4:  # Period format like "2024-Q3" or "2023"
                    filing_data = await sec_provider.get_filing_by_period(ticker, filing_date, form_type)
                    if filing_data:
                        return json.dumps({
                            "ticker": ticker,
                            "period_requested": filing_date,
                            "filing_data": filing_data.__dict__,
                            "data_source": "SEC EDGAR Temporal"
                        }, indent=2)
                else:
                    # Specific date format - get complete document
                    complete_doc = await sec_provider.get_complete_document_content(ticker, form_type, filing_date)
                    return json.dumps(complete_doc, indent=2)
            elif date_range:
                # Get filings within date range
                filings = await sec_provider.get_multiple_filings(ticker, [form_type], count * 2)
                # Filter by date range (simplified implementation)
                start_date = date_range.get('start_date')
                end_date = date_range.get('end_date')
                if start_date and end_date:
                    filtered_filings = []
                    for filing in filings:
                        if start_date <= filing.filing_date <= end_date:
                            filtered_filings.append(filing)
                    filings = filtered_filings[:count]

                return json.dumps({
                    "ticker": ticker,
                    "date_range": date_range,
                    "filings_count": len(filings),
                    "filings_data": [f.__dict__ for f in filings],
                    "data_source": "SEC EDGAR Date Range"
                }, indent=2)
            else:
                # Standard retrieval
                if count == 1:
                    filing_data = await sec_provider.get_latest_filing(ticker, form_type)
                    if filing_data:
                        # Return complete document if requested
                        if full_document:
                            complete_doc = await sec_provider.get_complete_document_content(ticker, form_type, "latest")
                            return json.dumps(complete_doc, indent=2)

                        return json.dumps({
                            "ticker": ticker,
                            "filing_data": filing_data.__dict__,
                            "data_source": "SEC EDGAR"
                        }, indent=2)
                else:
                    filings_data = await sec_provider.get_multiple_filings(ticker, [form_type], count)
                    return json.dumps({
                        "ticker": ticker,
                        "filings_count": len(filings_data),
                        "filings_data": [f.__dict__ for f in filings_data],
                        "temporal_features": {
                            "supports_period_queries": True,
                            "supports_date_ranges": True,
                            "supports_full_documents": True
                        },
                        "data_source": "SEC EDGAR Enhanced"
                    }, indent=2)

            return f"No SEC filing data found for {ticker}"

        except Exception as e:
            return f"Error retrieving SEC data for {ticker}: {str(e)}"

    async def _analyze_financial_performance(self, parameters: Dict[str, Any]) -> str:
        """Analyze financial performance function implementation"""
        from .specialized_agents import FinancialPerformanceAnalysisAgent
        from config import Config

        ticker = parameters['ticker']
        analysis_type = parameters.get('analysis_type', 'comprehensive')

        try:
            config = Config()
            agent = FinancialPerformanceAnalysisAgent(
                self.bedrock_client, self.data_provider, config
            )

            result = await agent.analyze(ticker)

            return json.dumps({
                "ticker": ticker,
                "analysis_type": "financial_performance",
                "agent_result": {
                    "analysis": result.analysis,
                    "key_metrics": result.key_metrics,
                    "recommendations": result.recommendations,
                    "confidence": result.confidence
                },
                "data_source": "Financial Performance Agent"
            }, indent=2)

        except Exception as e:
            return f"Error in financial performance analysis for {ticker}: {str(e)}"

    async def _analyze_operational_metrics(self, parameters: Dict[str, Any]) -> str:
        """Analyze operational metrics function implementation"""
        from .specialized_agents import OperationalMetricsAgent
        from config import Config

        ticker = parameters['ticker']
        focus_area = parameters.get('focus_area', 'comprehensive')

        try:
            config = Config()
            agent = OperationalMetricsAgent(
                self.bedrock_client, self.data_provider, config
            )

            result = await agent.analyze(ticker)

            return json.dumps({
                "ticker": ticker,
                "analysis_type": "operational_metrics",
                "agent_result": {
                    "analysis": result.analysis,
                    "key_metrics": result.key_metrics,
                    "recommendations": result.recommendations,
                    "confidence": result.confidence
                },
                "data_source": "Operational Metrics Agent"
            }, indent=2)

        except Exception as e:
            return f"Error in operational metrics analysis for {ticker}: {str(e)}"

    async def _analyze_risk_factors(self, parameters: Dict[str, Any]) -> str:
        """Analyze risk factors function implementation"""
        from .risk_and_valuation_agents import RiskMonitoringAgent
        from config import Config

        ticker = parameters['ticker']
        risk_category = parameters.get('risk_category', 'comprehensive')

        try:
            config = Config()
            agent = RiskMonitoringAgent(
                self.bedrock_client, self.data_provider, config
            )

            result = await agent.analyze(ticker)

            return json.dumps({
                "ticker": ticker,
                "analysis_type": "risk_monitoring",
                "agent_result": {
                    "analysis": result.analysis,
                    "key_metrics": result.key_metrics,
                    "recommendations": result.recommendations,
                    "confidence": result.confidence
                },
                "data_source": "Risk Monitoring Agent"
            }, indent=2)

        except Exception as e:
            return f"Error in risk analysis for {ticker}: {str(e)}"

    async def _analyze_valuation_metrics(self, parameters: Dict[str, Any]) -> str:
        """Analyze valuation metrics function implementation"""
        from .risk_and_valuation_agents import ValuationAnalysisAgent
        from config import Config

        ticker = parameters['ticker']
        valuation_method = parameters.get('valuation_method', 'comprehensive')

        try:
            config = Config()
            agent = ValuationAnalysisAgent(
                self.bedrock_client, self.data_provider, config
            )

            result = await agent.analyze(ticker)

            return json.dumps({
                "ticker": ticker,
                "analysis_type": "valuation_analysis",
                "agent_result": {
                    "analysis": result.analysis,
                    "key_metrics": result.key_metrics,
                    "recommendations": result.recommendations,
                    "confidence": result.confidence
                },
                "data_source": "Valuation Analysis Agent"
            }, indent=2)

        except Exception as e:
            return f"Error in valuation analysis for {ticker}: {str(e)}"

    async def _analyze_strategic_position(self, parameters: Dict[str, Any]) -> str:
        """Analyze strategic position function implementation"""
        from .strategic_analysis_agent import StrategicAnalysisAgent
        from config import Config

        ticker = parameters['ticker']
        strategic_focus = parameters.get('strategic_focus', 'comprehensive')

        try:
            config = Config()
            agent = StrategicAnalysisAgent(
                self.bedrock_client, self.data_provider, config
            )

            result = await agent.analyze(ticker)

            return json.dumps({
                "ticker": ticker,
                "analysis_type": "strategic_analysis",
                "agent_result": {
                    "analysis": result.analysis,
                    "key_metrics": result.key_metrics,
                    "recommendations": result.recommendations,
                    "confidence": result.confidence
                },
                "data_source": "Strategic Analysis Agent"
            }, indent=2)

        except Exception as e:
            return f"Error in strategic analysis for {ticker}: {str(e)}"

    async def _compare_companies(self, parameters: Dict[str, Any]) -> str:
        """Compare companies function implementation"""
        tickers = parameters['tickers']
        comparison_metrics = parameters.get('comparison_metrics', ['revenue_growth', 'margins', 'valuation'])

        try:
            comparison_results = {}

            for ticker in tickers:
                # Get basic financial data for comparison
                stock_data = await self.data_provider.get_stock_data(ticker)
                financial_metrics = await self.data_provider.get_financial_metrics(ticker)

                if stock_data and financial_metrics:
                    comparison_results[ticker] = {
                        "current_price": stock_data.current_price,
                        "market_cap": stock_data.market_cap,
                        "pe_ratio": stock_data.pe_ratio,
                        "revenue_growth": financial_metrics.revenue_growth,
                        "gross_margin": financial_metrics.gross_margin,
                        "operating_margin": financial_metrics.operating_margin,
                        "roe": financial_metrics.roe,
                        "debt_to_equity": financial_metrics.debt_to_equity
                    }

            return json.dumps({
                "comparison_type": "multi_company",
                "tickers": tickers,
                "metrics_compared": comparison_metrics,
                "comparison_results": comparison_results,
                "data_source": "Comparative Analysis",
                "timestamp": "2024-01-01T00:00:00"  # Would use actual timestamp
            }, indent=2)

        except Exception as e:
            return f"Error in company comparison: {str(e)}"

    async def _get_temporal_financial_data(self, parameters: Dict[str, Any]) -> str:
        """Get temporal financial data with period specificity using enhanced data sources"""
        from datetime import datetime

        ticker = parameters['ticker']
        period_type = parameters.get('period_type', 'quarterly')
        specific_period = parameters.get('specific_period')
        comparison_periods = parameters.get('comparison_periods', [])
        metrics = parameters.get('metrics', ['revenue', 'net_income', 'operating_income', 'capex'])

        try:
            # Use the enhanced data provider for temporal data
            temporal_data = {}

            # Parse period specifications
            periods_to_analyze = []
            if specific_period:
                periods_to_analyze.append(specific_period)
            periods_to_analyze.extend(comparison_periods)

            # If no specific periods, get recent periods based on type
            if not periods_to_analyze:
                current_year = datetime.now().year
                if period_type == 'quarterly':
                    periods_to_analyze = [f"{current_year}-Q3", f"{current_year}-Q2", f"{current_year-1}-Q3"]
                elif period_type == 'annual':
                    periods_to_analyze = [str(current_year-1), str(current_year-2)]

            # Get temporal financial data for each period
            for period in periods_to_analyze:
                period_metrics = await self.data_provider.get_temporal_financial_metrics(ticker, period)
                if period_metrics and period_metrics.get('data_available'):
                    temporal_data[period] = period_metrics

            # If no temporal data available, try to get recent data
            if not temporal_data:
                # Fallback to current financial metrics
                current_metrics = await self.data_provider.get_financial_metrics(ticker)
                if current_metrics:
                    temporal_data['current'] = {
                        "ticker": ticker,
                        "period": "current",
                        "period_type": "ttm",
                        "metrics": {
                            "revenue": current_metrics.revenue_ttm,
                            "net_income": current_metrics.net_income,
                            "free_cash_flow": current_metrics.free_cash_flow,
                            "roe": current_metrics.roe,
                            "debt_to_equity": current_metrics.debt_to_equity
                        },
                        "data_available": True,
                        "timestamp": current_metrics.timestamp
                    }

            return json.dumps({
                "ticker": ticker,
                "analysis_type": "temporal_financial_data",
                "period_type": period_type,
                "periods_requested": periods_to_analyze,
                "periods_with_data": list(temporal_data.keys()),
                "temporal_data": temporal_data,
                "data_sources": ["yfinance temporal data", "SEC EDGAR filings"],
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            return f"Error retrieving temporal financial data for {ticker}: {str(e)}"

    async def _get_complete_sec_document(self, parameters: Dict[str, Any]) -> str:
        """Get complete SEC document with full content analysis"""
        from .sec_data_provider import EnhancedSECDataProvider
        from config import Config
        from datetime import datetime

        ticker = parameters['ticker']
        form_type = parameters.get('form_type', '10-K')
        filing_date = parameters.get('filing_date', 'latest')
        sections = parameters.get('sections', ['business', 'risk_factors', 'mda', 'financial_statements'])
        include_exhibits = parameters.get('include_exhibits', False)

        try:
            config = Config()
            sec_provider = EnhancedSECDataProvider(config)

            # Get the specific filing
            if filing_date == 'latest':
                filing_data = await sec_provider.get_latest_filing(ticker, form_type)
            else:
                # Get multiple filings and find the one closest to the specified date
                filings = await sec_provider.get_multiple_filings(ticker, [form_type], 10)
                filing_data = None
                for filing in filings:
                    if filing_date in filing.filing_date:
                        filing_data = filing
                        break

                if not filing_data and filings:
                    filing_data = filings[0]  # Fallback to latest

            if not filing_data:
                return f"No {form_type} filing found for {ticker}"

            # Extract complete document sections
            complete_document = {
                "filing_metadata": {
                    "ticker": filing_data.ticker,
                    "form_type": filing_data.form_type,
                    "filing_date": filing_data.filing_date,
                    "accession_number": filing_data.accession_number,
                    "period_end_date": filing_data.period_end_date
                },
                "document_sections": {},
                "financial_data": {
                    "revenue": filing_data.revenue,
                    "net_income": filing_data.net_income,
                    "total_assets": filing_data.total_assets,
                    "free_cash_flow": filing_data.free_cash_flow
                }
            }

            # Add requested sections
            if 'business' in sections:
                complete_document["document_sections"]["business_description"] = "Complete business section would be extracted here"

            if 'risk_factors' in sections:
                complete_document["document_sections"]["risk_factors"] = filing_data.risk_factors

            if 'mda' in sections:
                complete_document["document_sections"]["management_discussion"] = filing_data.management_discussion

            if 'financial_statements' in sections:
                complete_document["document_sections"]["financial_statements"] = {
                    "revenue": filing_data.revenue,
                    "net_income": filing_data.net_income,
                    "segment_data": filing_data.segment_data
                }

            if include_exhibits:
                complete_document["exhibits"] = "Exhibit analysis would be included here"

            return json.dumps({
                "ticker": ticker,
                "analysis_type": "complete_sec_document",
                "document_type": form_type,
                "complete_document": complete_document,
                "sections_included": sections,
                "data_source": "SEC EDGAR Complete Document Analysis",
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            return f"Error retrieving complete SEC document for {ticker}: {str(e)}"

    def _enhance_system_message_for_temporal(self, system_message: Optional[str], query_intent: Optional[Any]) -> str:
        """Enhance system message based on temporal specificity detected in query intent"""
        base_message = system_message or "You are a financial analyst with access to comprehensive financial data and tools."

        if not query_intent or not hasattr(query_intent, 'temporal_specificity'):
            return base_message

        if query_intent.temporal_specificity and query_intent.temporal_periods:
            temporal_enhancement = f"""

TEMPORAL DATA SPECIFICITY DETECTED:
- Specific periods mentioned: {', '.join(query_intent.temporal_periods)}
- Use temporal-specific functions when available:
  * get_temporal_financial_data() for period-specific financial metrics
  * get_sec_filing_data() with filing_date parameter for specific periods
  * get_complete_sec_document() for comprehensive document analysis

CRITICAL TEMPORAL GUIDELINES:
- Always specify exact periods when retrieving data (e.g., "2024-Q3", "2023")
- Include filing dates and period end dates in your analysis
- Provide source attribution with document dates and accession numbers
- When comparing periods, use the temporal_financial_data function with comparison_periods
- For queries mentioning specific years or quarters, prioritize temporal functions over generic ones

EXAMPLE TEMPORAL USAGE:
- For "2023 revenue": Use get_temporal_financial_data(specific_period="2023")
- For "Q3 2024 vs Q3 2023": Use comparison_periods=["2024-Q3", "2023-Q3"]
- For "2023 10-K": Use get_sec_filing_data(filing_date="2023")
"""
            return base_message + temporal_enhancement

        return base_message
