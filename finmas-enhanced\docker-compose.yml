version: '3.8'

services:
  finmas-enhanced:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: finmas-enhanced
    restart: unless-stopped
    
    # Environment variables
    environment:
      # AWS Configuration (set these in .env file)
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN:-}
      - AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION:-us-east-1}
      
      # Bedrock Configuration
      - BEDROCK_MODEL=${BEDROCK_MODEL:-us.anthropic.claude-3-5-sonnet-20241022-v2:0}
      - TEMPERATURE=${TEMPERATURE:-0.1}
      - MAX_TOKENS=${MAX_TOKENS:-4000}
      
      # Cost and Logging
      - BUDGET_LIMIT=${BUDGET_LIMIT:-10.0}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      
      # Optional API Keys for enhanced data sources
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY:-}
      - FINNHUB_API_KEY=${FINNHUB_API_KEY:-}
      - NEWS_API_KEY=${NEWS_API_KEY:-}
    
    # Volumes for persistent data
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - finmas-cache:/app/.cache
    
    # Interactive terminal
    stdin_open: true
    tty: true
    
    # Network configuration
    networks:
      - finmas-network
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Optional: Redis for caching (if needed in future)
  redis:
    image: redis:7-alpine
    container_name: finmas-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    networks:
      - finmas-network
    profiles:
      - cache

  # Optional: Monitoring with Prometheus (if needed)
  prometheus:
    image: prom/prometheus:latest
    container_name: finmas-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - finmas-network
    profiles:
      - monitoring

volumes:
  finmas-cache:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local

networks:
  finmas-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
