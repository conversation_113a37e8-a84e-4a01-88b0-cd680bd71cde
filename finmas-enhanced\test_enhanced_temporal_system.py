#!/usr/bin/env python3
"""
Enhanced Temporal Financial Analysis System Test

This script demonstrates the enhanced temporal data specificity and 
complete document retrieval capabilities based on the AI prompts log analysis.
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any

# Import system components
from src.function_calling import FunctionCallOrchestrator
from src.bedrock_client import BedrockClient
from src.data_sources import FinancialDataProvider
from config import Config
from utils.logging_utils import get_logger

logger = get_logger()

async def test_temporal_data_specificity():
    """Test enhanced temporal data specificity features"""
    print("\n" + "="*80)
    print("TESTING TEMPORAL DATA SPECIFICITY")
    print("="*80)
    
    config = Config()
    bedrock_client = BedrockClient(config)
    
    async with FinancialDataProvider(config) as data_provider:
        orchestrator = FunctionCallOrchestrator(bedrock_client, data_provider)
        
        # Test 1: Specific period financial data
        print("\n1. Testing specific period financial data retrieval...")
        
        messages = [{
            "role": "user",
            "content": [{"text": "Get financial data for MSFT for Q3 2024 compared to Q3 2023, focusing on revenue and CapEx"}]
        }]
        
        system_message = """You are a financial analyst with access to temporal financial data. 
        Use the get_temporal_financial_data function to retrieve period-specific financial metrics.
        Always specify exact periods when available."""
        
        try:
            response, function_calls, cost_info = await orchestrator.execute_with_functions(
                messages=messages,
                system_message=system_message,
                max_iterations=3
            )
            
            print(f"Response: {response[:500]}...")
            print(f"Function calls made: {len(function_calls)}")
            print(f"Total cost: ${cost_info['total_cost']:.4f}")
            
        except Exception as e:
            print(f"Error in temporal data test: {str(e)}")

async def test_complete_document_retrieval():
    """Test complete SEC document retrieval capabilities"""
    print("\n" + "="*80)
    print("TESTING COMPLETE DOCUMENT RETRIEVAL")
    print("="*80)
    
    config = Config()
    bedrock_client = BedrockClient(config)
    
    async with FinancialDataProvider(config) as data_provider:
        orchestrator = FunctionCallOrchestrator(bedrock_client, data_provider)
        
        # Test 2: Complete document analysis
        print("\n2. Testing complete SEC document retrieval...")
        
        messages = [{
            "role": "user",
            "content": [{"text": "Get the complete 10-K document for AAPL including all risk factors and management discussion sections"}]
        }]
        
        system_message = """You are a financial analyst with access to complete SEC documents. 
        Use the get_complete_sec_document function to retrieve full document content with all sections.
        Always include source attribution and document metadata."""
        
        try:
            response, function_calls, cost_info = await orchestrator.execute_with_functions(
                messages=messages,
                system_message=system_message,
                max_iterations=3
            )
            
            print(f"Response: {response[:500]}...")
            print(f"Function calls made: {len(function_calls)}")
            print(f"Total cost: ${cost_info['total_cost']:.4f}")
            
        except Exception as e:
            print(f"Error in document retrieval test: {str(e)}")

async def test_enhanced_sec_filing_analysis():
    """Test enhanced SEC filing analysis with temporal specificity"""
    print("\n" + "="*80)
    print("TESTING ENHANCED SEC FILING ANALYSIS")
    print("="*80)
    
    config = Config()
    bedrock_client = BedrockClient(config)
    
    async with FinancialDataProvider(config) as data_provider:
        orchestrator = FunctionCallOrchestrator(bedrock_client, data_provider)
        
        # Test 3: Enhanced SEC filing with date range
        print("\n3. Testing SEC filing analysis with date range...")
        
        messages = [{
            "role": "user",
            "content": [{"text": "Analyze SEC filings for MSFT from 2023 to 2024, focusing on CapEx alignment with growth initiatives"}]
        }]
        
        system_message = """You are a financial analyst with access to enhanced SEC filing data. 
        Use the get_sec_filing_data function with temporal parameters to retrieve filings for specific periods.
        Include filing dates, period end dates, and source attribution in your analysis."""
        
        try:
            response, function_calls, cost_info = await orchestrator.execute_with_functions(
                messages=messages,
                system_message=system_message,
                max_iterations=3
            )
            
            print(f"Response: {response[:500]}...")
            print(f"Function calls made: {len(function_calls)}")
            print(f"Total cost: ${cost_info['total_cost']:.4f}")
            
        except Exception as e:
            print(f"Error in enhanced SEC filing test: {str(e)}")

async def test_comparative_temporal_analysis():
    """Test comparative analysis across multiple time periods"""
    print("\n" + "="*80)
    print("TESTING COMPARATIVE TEMPORAL ANALYSIS")
    print("="*80)
    
    config = Config()
    bedrock_client = BedrockClient(config)
    
    async with FinancialDataProvider(config) as data_provider:
        orchestrator = FunctionCallOrchestrator(bedrock_client, data_provider)
        
        # Test 4: Multi-period comparative analysis
        print("\n4. Testing multi-period comparative analysis...")
        
        messages = [{
            "role": "user",
            "content": [{"text": "Compare MSFT and AAPL CapEx trends over 2023 and 2024, analyzing alignment with revenue growth"}]
        }]
        
        system_message = """You are a financial analyst specializing in comparative temporal analysis. 
        Use temporal financial data functions and SEC filing analysis to compare companies across specific time periods.
        Always provide period-specific data with exact dates and source attribution."""
        
        try:
            response, function_calls, cost_info = await orchestrator.execute_with_functions(
                messages=messages,
                system_message=system_message,
                max_iterations=5
            )
            
            print(f"Response: {response[:500]}...")
            print(f"Function calls made: {len(function_calls)}")
            print(f"Total cost: ${cost_info['total_cost']:.4f}")
            
        except Exception as e:
            print(f"Error in comparative temporal analysis test: {str(e)}")

async def demonstrate_enhanced_capabilities():
    """Demonstrate the enhanced capabilities addressing log analysis findings"""
    print("\n" + "="*80)
    print("ENHANCED FINANCIAL ANALYSIS SYSTEM DEMONSTRATION")
    print("Addressing AI Prompts Log Analysis Findings")
    print("="*80)
    
    print("\nEnhanced Capabilities:")
    print("1. ✅ Temporal Data Specificity - Request specific quarters/years")
    print("2. ✅ Complete Document Retrieval - Full SEC filing analysis")
    print("3. ✅ Enhanced Query Quality - Period-specific context")
    print("4. ✅ Source Attribution - Document dates and filing references")
    print("5. ✅ Comparative Analysis - Multi-period comparisons")
    
    # Run all tests
    await test_temporal_data_specificity()
    await test_complete_document_retrieval()
    await test_enhanced_sec_filing_analysis()
    await test_comparative_temporal_analysis()
    
    print("\n" + "="*80)
    print("DEMONSTRATION COMPLETE")
    print("="*80)
    print("\nKey Improvements Implemented:")
    print("• Enhanced function calling with temporal parameters")
    print("• Complete SEC document retrieval capabilities")
    print("• Period-specific financial data extraction")
    print("• Date range filtering for SEC filings")
    print("• Full document vs excerpt options")
    print("• Improved source attribution and metadata")

if __name__ == "__main__":
    asyncio.run(demonstrate_enhanced_capabilities())
