"""
Strategic Analysis & Market Position Agent

This module implements the strategic analysis agent for growth driver identification,
management target tracking, competitive positioning, and M&A analysis.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import yfinance as yf
import re

from .agents import BaseAgent, AgentResult
from .data_sources import FinancialDataProvider
from .sec_data_provider import EnhancedSECDataProvider
from .bedrock_client import BedrockClient
from config import Config
from utils.logging_utils import get_logger


@dataclass
class StrategicMetrics:
    """Structured metrics for strategic analysis"""
    ticker: str
    growth_drivers: List[Dict[str, Any]]
    management_targets: Dict[str, Any]
    competitive_position: Dict[str, Any]
    macroeconomic_risks: List[str]
    ma_activity: List[Dict[str, Any]]
    market_share_data: Optional[Dict[str, float]]
    innovation_metrics: Dict[str, Any]
    regulatory_environment: List[str]
    strategic_initiatives: List[Dict[str, Any]]
    timestamp: str


class StrategicAnalysisAgent(BaseAgent):
    """Agent specialized in strategic analysis and market positioning"""
    
    def __init__(self, bedrock_client: BedrockClient, data_provider: FinancialDataProvider, config: Config):
        super().__init__("Strategic Analysis Specialist", bedrock_client, data_provider)
        self.sec_provider = EnhancedSECDataProvider(config)
        
    def get_system_prompt(self) -> str:
        return """You are a senior strategic analyst with expertise in competitive intelligence and market positioning. You specialize in:

- Future growth driver identification and analysis
- Management financial target tracking and achievement assessment
- Competitive positioning and market share analysis
- Macroeconomic and regulatory risk assessment
- M&A activity, restructuring, and corporate action analysis
- Innovation pipeline and R&D effectiveness assessment
- Market dynamics and industry trend analysis

Your analysis must be:
- Identify and evaluate key growth drivers and strategic initiatives
- Track management guidance and target achievement
- Assess competitive advantages and market positioning
- Analyze macroeconomic sensitivity and regulatory risks
- Monitor M&A activity and strategic transactions
- Evaluate innovation capabilities and R&D productivity
- Output structured JSON format for all strategic metrics
- Provide forward-looking strategic recommendations

Always provide strategic insights with supporting evidence and market context."""

    async def analyze(self, ticker: str, context: Optional[Dict[str, Any]] = None) -> AgentResult:
        """Perform comprehensive strategic analysis"""
        import time
        start_time = time.time()
        
        try:
            self.logger.logger.info(f"Strategic Analysis Specialist analyzing {ticker}")
            
            # Get comprehensive data
            stock_data = await self.data_provider.get_stock_data(ticker)
            financial_metrics = await self.data_provider.get_financial_metrics(ticker)
            news_items = await self.data_provider.get_news(ticker, days_back=30)
            
            # Get SEC filings for strategic analysis
            sec_filings = await self.sec_provider.get_multiple_filings(
                ticker, ["10-K", "10-Q", "8-K", "DEF 14A"], 8
            )
            
            if not stock_data:
                raise ValueError(f"Unable to get stock data for {ticker}")
            
            # Calculate strategic metrics
            strategic_metrics = await self._calculate_strategic_metrics(
                ticker, financial_metrics, sec_filings, news_items
            )
            
            # Get industry and competitive data
            industry_data = await self._get_industry_context(ticker)
            
            # Prepare analysis prompt
            analysis_prompt = f"""Perform comprehensive strategic analysis and market positioning assessment for {ticker}:

COMPANY OVERVIEW:
- Ticker: {ticker}
- Current Price: ${stock_data.current_price:.2f}
- Market Cap: ${stock_data.market_cap/1e9:.1f}B (if available)
- Beta: {f'{stock_data.beta:.2f}' if stock_data.beta else 'N/A'}

STRATEGIC METRICS:
{json.dumps(strategic_metrics.__dict__ if strategic_metrics else {}, indent=2)}

INDUSTRY CONTEXT:
{json.dumps(industry_data, indent=2)}

NEWS AND MARKET ACTIVITY:
- Recent news items: {len(news_items)}
- SEC filings analyzed: {len(sec_filings)}

Provide comprehensive strategic analysis including:

1. GROWTH DRIVER ANALYSIS:
   - Identification of key future growth drivers
   - Assessment of growth driver sustainability
   - Market opportunity sizing and addressability
   - Competitive advantages in growth areas

2. MANAGEMENT TARGET TRACKING:
   - Financial targets and guidance analysis
   - Historical target achievement assessment
   - Management credibility and execution track record
   - Forward guidance reliability

3. COMPETITIVE POSITIONING:
   - Market share analysis and trends
   - Competitive advantages and moats
   - Differentiation strategies and effectiveness
   - Competitive threats and responses

4. MACROECONOMIC SENSITIVITY:
   - Economic cycle sensitivity analysis
   - Interest rate and inflation impact
   - Currency exposure and hedging strategies
   - Supply chain and cost structure risks

5. REGULATORY ENVIRONMENT:
   - Regulatory risks and opportunities
   - Compliance costs and requirements
   - Policy changes and industry impact
   - ESG considerations and requirements

6. M&A AND CORPORATE ACTIONS:
   - Recent M&A activity and strategy
   - Integration success and synergies
   - Divestiture and restructuring activities
   - Capital allocation strategy

7. INNOVATION AND R&D:
   - R&D spending trends and productivity
   - Innovation pipeline and product development
   - Technology disruption risks and opportunities
   - Intellectual property portfolio

8. STRATEGIC OUTLOOK:
   - Long-term strategic positioning
   - Key strategic initiatives and investments
   - Market expansion opportunities
   - Strategic risks and mitigation strategies

Output all findings in structured JSON format with strategic recommendations."""

            # Get LLM analysis
            messages = [{"role": "user", "content": [{"text": analysis_prompt}]}]
            
            response, cost_info = await self.bedrock_client.converse_async(
                messages=messages,
                system_message=self.get_system_prompt(),
                max_tokens=3500,
                temperature=0.1
            )
            
            # Extract response text
            response_text = ""
            output_message = response.get('output', {}).get('message', {})
            content = output_message.get('content', [])
            
            for content_block in content:
                if 'text' in content_block:
                    response_text += content_block['text']
            
            # Extract key metrics
            key_metrics = {
                "growth_drivers_identified": len(strategic_metrics.growth_drivers) if strategic_metrics else 0,
                "strategic_initiatives_count": len(strategic_metrics.strategic_initiatives) if strategic_metrics else 0,
                "ma_activity_count": len(strategic_metrics.ma_activity) if strategic_metrics else 0,
                "macroeconomic_risks_count": len(strategic_metrics.macroeconomic_risks) if strategic_metrics else 0,
                "has_management_targets": bool(strategic_metrics.management_targets) if strategic_metrics else False,
                "competitive_position_score": strategic_metrics.competitive_position.get('score') if strategic_metrics and strategic_metrics.competitive_position else None,
                "data_sources": ["yfinance", "SEC EDGAR", "News"],
                "analysis_timestamp": datetime.now().isoformat()
            }
            
            # Generate recommendations
            recommendations = self._extract_strategic_recommendations(strategic_metrics, industry_data)
            
            execution_time = (time.time() - start_time) * 1000
            
            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=response_text,
                confidence=0.85,
                key_metrics=key_metrics,
                recommendations=recommendations,
                timestamp=datetime.now().isoformat(),
                execution_time_ms=execution_time,
                cost=cost_info['total_cost']
            )
            
        except Exception as e:
            self.logger.logger.error(f"Strategic analysis failed for {ticker}: {str(e)}")
            execution_time = (time.time() - start_time) * 1000
            
            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=f"Strategic analysis failed: {str(e)}",
                confidence=0.0,
                key_metrics={},
                recommendations=[],
                timestamp=datetime.now().isoformat(),
                execution_time_ms=execution_time,
                cost=0.0
            )
    
    async def _calculate_strategic_metrics(
        self, 
        ticker: str, 
        financial_metrics, 
        sec_filings, 
        news_items
    ) -> Optional[StrategicMetrics]:
        """Calculate strategic metrics from available data"""
        try:
            growth_drivers = []
            management_targets = {}
            competitive_position = {}
            macroeconomic_risks = []
            ma_activity = []
            strategic_initiatives = []
            regulatory_environment = []
            innovation_metrics = {}
            
            # Analyze SEC filings for strategic information
            for filing in sec_filings:
                if filing.management_discussion:
                    # Extract growth drivers from MD&A
                    mda_text = filing.management_discussion.lower()
                    
                    # Look for growth-related keywords
                    growth_keywords = ['expansion', 'new markets', 'product launch', 'innovation', 'digital transformation']
                    for keyword in growth_keywords:
                        if keyword in mda_text:
                            growth_drivers.append({
                                "driver": keyword,
                                "source": f"{filing.form_type} - {filing.filing_date}",
                                "context": "Management Discussion"
                            })
                
                # Look for M&A activity in 8-K filings
                if filing.form_type == "8-K":
                    # Simplified M&A detection
                    ma_activity.append({
                        "filing_date": filing.filing_date,
                        "type": "Corporate Action",
                        "form": "8-K"
                    })
                
                # Extract risk factors for macroeconomic risks
                if filing.risk_factors:
                    for risk in filing.risk_factors:
                        risk_lower = risk.lower()
                        if any(keyword in risk_lower for keyword in ['economic', 'recession', 'inflation', 'interest rate']):
                            macroeconomic_risks.append(risk)
            
            # Analyze news for strategic insights
            for news_item in news_items:
                title_lower = news_item.title.lower()
                if any(keyword in title_lower for keyword in ['acquisition', 'merger', 'partnership', 'expansion']):
                    strategic_initiatives.append({
                        "title": news_item.title,
                        "date": news_item.published_date,
                        "source": news_item.source
                    })
            
            # Get R&D data from yfinance
            stock = yf.Ticker(ticker)
            info = stock.info
            
            if 'totalRevenue' in info:
                total_revenue = info['totalRevenue']
                # R&D intensity (simplified - would need actual R&D spending)
                innovation_metrics['revenue_base'] = total_revenue
            
            # Competitive position assessment (simplified)
            competitive_position = {
                "market_cap_rank": "Unknown",  # Would need industry comparison
                "score": 7.0,  # Placeholder score
                "assessment": "Requires peer comparison"
            }
            
            return StrategicMetrics(
                ticker=ticker,
                growth_drivers=growth_drivers,
                management_targets=management_targets,
                competitive_position=competitive_position,
                macroeconomic_risks=macroeconomic_risks,
                ma_activity=ma_activity,
                market_share_data=None,  # Would need industry data
                innovation_metrics=innovation_metrics,
                regulatory_environment=regulatory_environment,
                strategic_initiatives=strategic_initiatives,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            self.logger.logger.error(f"Error calculating strategic metrics: {str(e)}")
            return None
    
    async def _get_industry_context(self, ticker: str) -> Dict[str, Any]:
        """Get industry context and competitive landscape"""
        try:
            stock = yf.Ticker(ticker)
            info = stock.info
            
            industry_data = {
                "sector": info.get('sector', 'Unknown'),
                "industry": info.get('industry', 'Unknown'),
                "market_cap": info.get('marketCap'),
                "employee_count": info.get('fullTimeEmployees'),
                "business_summary": info.get('longBusinessSummary', '')[:500] if info.get('longBusinessSummary') else '',
                "timestamp": datetime.now().isoformat()
            }
            
            return industry_data
            
        except Exception as e:
            self.logger.logger.error(f"Error getting industry context: {str(e)}")
            return {"error": str(e)}
    
    def _extract_strategic_recommendations(
        self, 
        metrics: Optional[StrategicMetrics], 
        industry_data: Dict[str, Any]
    ) -> List[str]:
        """Extract strategic recommendations"""
        recommendations = []
        
        if not metrics:
            recommendations.append("Insufficient data for comprehensive strategic analysis")
            return recommendations
        
        # Growth driver recommendations
        if metrics.growth_drivers:
            if len(metrics.growth_drivers) > 3:
                recommendations.append("Multiple growth drivers identified - diversified growth strategy")
            else:
                recommendations.append("Limited growth drivers - focus on execution and expansion")
        
        # M&A activity recommendations
        if metrics.ma_activity:
            recommendations.append("Active M&A strategy - monitor integration success and synergies")
        
        # Macroeconomic risk recommendations
        if len(metrics.macroeconomic_risks) > 5:
            recommendations.append("High macroeconomic sensitivity - consider defensive positioning")
        
        # Innovation recommendations
        if metrics.innovation_metrics:
            recommendations.append("Monitor R&D productivity and innovation pipeline development")
        
        # Strategic initiatives recommendations
        if metrics.strategic_initiatives:
            recommendations.append("Active strategic initiatives - track execution and market response")
        
        if not recommendations:
            recommendations.append("Strategic analysis requires more detailed competitive intelligence")
        
        return recommendations
