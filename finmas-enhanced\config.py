"""
Configuration for FinMAS Enhanced Command-Line System
"""

import os
from typing import Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass


@dataclass
class Config:
    """Configuration class for FinMAS Enhanced"""

    # AWS Configuration
    aws_access_key_id: Optional[str] = None
    aws_secret_access_key: Optional[str] = None
    aws_session_token: Optional[str] = None
    aws_region: str = "us-east-1"

    # Bedrock Configuration
    bedrock_model: str = "us.anthropic.claude-3-5-sonnet-20241022-v2:0"
    temperature: float = 0.1
    max_tokens: int = 4000
    max_retries: int = 3
    retry_delay: float = 1.0

    # Financial Data Configuration
    data_sources: Dict[str, bool] = None
    cache_duration: int = 300  # 5 minutes

    # Cost Tracking
    budget_limit: float = 10.0
    cost_alerts: bool = True

    # Logging
    log_level: str = "INFO"
    log_dir: str = "logs"

    def __post_init__(self):
        """Initialize configuration from environment variables"""
        # AWS credentials
        self.aws_access_key_id = os.getenv("AWS_ACCESS_KEY_ID")
        self.aws_secret_access_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        self.aws_session_token = os.getenv("AWS_SESSION_TOKEN")
        self.aws_region = os.getenv("AWS_DEFAULT_REGION", self.aws_region)

        # Bedrock settings
        self.bedrock_model = os.getenv("BEDROCK_MODEL", self.bedrock_model)
        self.temperature = float(os.getenv("TEMPERATURE", self.temperature))
        self.max_tokens = int(os.getenv("MAX_TOKENS", self.max_tokens))

        # Cost settings
        self.budget_limit = float(os.getenv("BUDGET_LIMIT", self.budget_limit))

        # Logging
        self.log_level = os.getenv("LOG_LEVEL", self.log_level)

        # Data sources
        if self.data_sources is None:
            self.data_sources = {
                "yfinance": True,
                "alpha_vantage": os.getenv("ALPHA_VANTAGE_API_KEY") is not None,
                "finnhub": os.getenv("FINNHUB_API_KEY") is not None,
                "news_api": os.getenv("NEWS_API_KEY") is not None
            }

        # Create directories
        Path(self.log_dir).mkdir(exist_ok=True)

    def validate(self) -> bool:
        """Validate configuration"""
        if not self.aws_access_key_id or not self.aws_secret_access_key:
            return False

        if self.temperature < 0 or self.temperature > 1:
            return False

        if self.max_tokens < 100 or self.max_tokens > 8000:
            return False

        return True

    def get_aws_session_kwargs(self) -> Dict[str, Any]:
        """Get AWS session configuration"""
        kwargs = {
            "region_name": self.aws_region
        }

        if self.aws_access_key_id and self.aws_secret_access_key:
            kwargs.update({
                "aws_access_key_id": self.aws_access_key_id,
                "aws_secret_access_key": self.aws_secret_access_key
            })

            if self.aws_session_token:
                kwargs["aws_session_token"] = self.aws_session_token

        return kwargs


# Model pricing information (per 1000 tokens)
MODEL_PRICING = {
    "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {
        "input": 0.003,   # $3 per 1M input tokens
        "output": 0.015   # $15 per 1M output tokens
    },
    "anthropic.claude-3-sonnet-20240229-v1:0": {
        "input": 0.003,
        "output": 0.015
    },
    "anthropic.claude-3-haiku-20240307-v1:0": {
        "input": 0.00025,
        "output": 0.00125
    }
}

# Available models
AVAILABLE_MODELS = [
    {
        "id": "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
        "name": "Claude 3.5 Sonnet (Latest)",
        "description": "Most capable model, best for complex analysis",
        "cost_tier": "premium"
    },
    {
        "id": "anthropic.claude-3-sonnet-20240229-v1:0",
        "name": "Claude 3 Sonnet",
        "description": "Balanced performance and cost",
        "cost_tier": "standard"
    },
    {
        "id": "anthropic.claude-3-haiku-20240307-v1:0",
        "name": "Claude 3 Haiku",
        "description": "Fastest and most cost-effective",
        "cost_tier": "economy"
    }
]
