#!/usr/bin/env python3
"""
Test script to verify logging duplication fix
"""

import sys
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from utils.logging_utils import get_logger, check_logger_status, reset_logger


def test_logging_fix():
    """Test that logging duplication is fixed"""
    print("🔍 Testing Logging Fix")
    print("=" * 50)
    
    # Reset logger to start fresh
    reset_logger()
    
    # Test 1: Single logger instance
    print("\n1. Testing single logger instance...")
    logger1 = get_logger()
    logger2 = get_logger()
    logger3 = get_logger()
    
    print(f"   Logger1 ID: {id(logger1)}")
    print(f"   Logger2 ID: {id(logger2)}")
    print(f"   Logger3 ID: {id(logger3)}")
    print(f"   Same instance: {logger1 is logger2 is logger3}")
    
    # Test 2: Handler count
    print("\n2. Testing handler count...")
    status = check_logger_status()
    print(f"   Handler count: {status['handler_count']}")
    print(f"   Handler types: {status['handler_types']}")
    print(f"   Propagate: {status['propagate']}")
    
    # Test 3: Log message (should appear only once)
    print("\n3. Testing log message (should appear only once):")
    logger1.logger.info("This is a test message - should appear only once")
    
    # Test 4: Multiple components using logger
    print("\n4. Testing multiple components...")
    
    # Simulate multiple components getting logger
    from src.data_sources import FinancialDataProvider
    from src.bedrock_client import BedrockClient
    from config import Config
    
    config = Config()
    
    # These should all use the same logger instance
    bedrock_client = BedrockClient(config)
    data_provider = FinancialDataProvider(config)
    
    print("   Components initialized")
    
    # Check status after component initialization
    status_after = check_logger_status()
    print(f"   Handler count after components: {status_after['handler_count']}")
    print(f"   Handler types after components: {status_after['handler_types']}")
    
    # Test 5: Log from different components
    print("\n5. Testing logs from different components (each should appear only once):")
    bedrock_client.logger.logger.info("Message from Bedrock client")
    data_provider.logger.logger.info("Message from Data provider")
    logger1.logger.info("Message from main logger")
    
    print("\n✅ Logging test completed!")
    print(f"   Final handler count: {check_logger_status()['handler_count']}")
    
    return status_after['handler_count'] <= 2  # Should have at most 2 handlers (file + console)


if __name__ == "__main__":
    success = test_logging_fix()
    if success:
        print("\n🎉 Logging fix successful - no duplicate logs!")
    else:
        print("\n❌ Logging issue still exists - check handler configuration")
