2025-06-21 12:07:52,614 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_120752
2025-06-21 12:07:52,619 - finmas_enhanced - INFO - FinMAS Enhanced Orchestrator initialized
2025-06-21 12:07:56,345 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_120756
2025-06-21 12:07:56,350 - finmas_enhanced - INFO - FinMAS Enhanced Orchestrator initialized
2025-06-21 12:07:56,364 - finmas_enhanced - INFO - Starting complete analysis for AAPL
2025-06-21 12:07:56,727 - finmas_enhanced - INFO - Enhanced Bedrock client initialized with model: us.anthropic.claude-3-5-sonnet-20241022-v2:0
2025-06-21 12:07:56,727 - finmas_enhanced - INFO - Bedrock client initialized with model: us.anthropic.claude-3-5-sonnet-20241022-v2:0
2025-06-21 12:07:56,727 - finmas_enhanced - INFO - Financial tools prepared for AAPL
2025-06-21 12:07:56,733 - finmas_enhanced - INFO - SEC integration skipped for demo mode
2025-06-21 12:07:56,735 - finmas_enhanced - INFO - Enhanced Financial Crew initialized for AAPL
2025-06-21 12:07:56,736 - finmas_enhanced - INFO - Starting complete financial analysis for AAPL
2025-06-21 12:07:56,740 - finmas_enhanced - ERROR - Complete analysis failed for AAPL after 2.01ms: 1 validation error for Agent
tools.0
  Input should be a valid dictionary or instance of BaseTool [type=model_type, input_value=<main.MockTechnicalAnalys...t at 0x000002131A4EFB10>, input_type=MockTechnicalAnalysisTool]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
2025-06-21 12:07:56,745 - finmas_enhanced - INFO - Analysis completed for AAPL - Success: False, Cost: $0.0000, Duration: 2.01ms
2025-06-21 12:07:56,745 - finmas_enhanced - INFO - Function main.run_complete_analysis completed in 380.94ms
2025-06-21 12:07:57,762 - finmas_enhanced - INFO - Session data saved to finmas-enhanced\logs\session_data_20250621_120756.json
2025-06-21 12:07:57,999 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_120757
2025-06-21 12:07:58,004 - finmas_enhanced - INFO - FinMAS Enhanced Orchestrator initialized
