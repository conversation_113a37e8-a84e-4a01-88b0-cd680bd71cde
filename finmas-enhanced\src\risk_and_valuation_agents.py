"""
Risk Monitoring and Valuation Analysis Agents

This module implements specialized agents for risk assessment and valuation analysis
with comprehensive SEC filing analysis and real-time data integration.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import yfinance as yf
import re

from .agents import BaseAgent, AgentResult
from .data_sources import FinancialDataProvider
from .sec_data_provider import EnhancedSECDataProvider
from .bedrock_client import BedrockClient
from config import Config
from utils.logging_utils import get_logger


@dataclass
class RiskMetrics:
    """Structured metrics for risk analysis"""
    ticker: str
    going_concern_warnings: List[str]
    material_weaknesses: List[str]
    executive_changes: List[Dict[str, Any]]
    litigation_risks: List[str]
    related_party_transactions: List[Dict[str, Any]]
    accounting_irregularities: List[str]
    impairment_charges: List[Dict[str, Any]]
    risk_score: Optional[float]
    timestamp: str


@dataclass
class ValuationMetrics:
    """Structured metrics for valuation analysis"""
    ticker: str
    tangible_book_value_per_share: Optional[float]
    insider_ownership_percent: Optional[float]
    insider_trading_activity: List[Dict[str, Any]]
    share_buyback_activity: Dict[str, Any]
    debt_to_equity_ratio: Optional[float]
    roic_current: Optional[float]
    roe_current: Optional[float]
    dividend_yield_current: Optional[float]
    dividend_policy_changes: List[str]
    revenue_cagr_5yr: Optional[float]
    earnings_cagr_5yr: Optional[float]
    timestamp: str


class RiskMonitoringAgent(BaseAgent):
    """Agent specialized in risk monitoring and red flag detection"""
    
    def __init__(self, bedrock_client: BedrockClient, data_provider: FinancialDataProvider, config: Config):
        super().__init__("Risk Monitoring Analyst", bedrock_client, data_provider)
        self.sec_provider = EnhancedSECDataProvider(config)
        
    def get_system_prompt(self) -> str:
        return """You are a senior risk analyst with expertise in financial risk assessment and red flag detection. You specialize in:

- Going concern warnings detection and analysis
- Material weakness in internal controls identification
- C-level executive turnover tracking and impact assessment
- Legal investigations and litigation monitoring
- Related-party transaction analysis and conflict detection
- Non-GAAP accounting irregularities detection
- Impairment charges and write-offs analysis

Your analysis must be:
- Identify and categorize specific risk factors from SEC filings
- Assess severity and potential impact of identified risks
- Track executive changes and their implications
- Monitor litigation and regulatory issues
- Detect accounting quality concerns and irregularities
- Provide risk scoring and prioritization
- Output structured JSON format for all risk metrics
- Include specific citations from SEC filings

Always provide specific risk assessments with supporting evidence from filings."""

    async def analyze(self, ticker: str, context: Optional[Dict[str, Any]] = None) -> AgentResult:
        """Perform comprehensive risk monitoring analysis"""
        import time
        start_time = time.time()
        
        try:
            self.logger.logger.info(f"Risk Monitoring Analyst analyzing {ticker}")
            
            # Get financial data
            stock_data = await self.data_provider.get_stock_data(ticker)
            financial_metrics = await self.data_provider.get_financial_metrics(ticker)
            
            # Get comprehensive SEC filings for risk analysis
            sec_filings = await self.sec_provider.get_multiple_filings(
                ticker, ["10-K", "10-Q", "8-K", "DEF 14A"], 8
            )
            
            if not stock_data:
                raise ValueError(f"Unable to get stock data for {ticker}")
            
            # Calculate risk metrics
            risk_metrics = await self._calculate_risk_metrics(ticker, financial_metrics, sec_filings)
            
            # Prepare analysis prompt
            analysis_prompt = f"""Perform comprehensive risk monitoring and red flag analysis for {ticker}:

COMPANY OVERVIEW:
- Ticker: {ticker}
- Current Price: ${stock_data.current_price:.2f}
- Market Cap: ${stock_data.market_cap/1e9:.1f}B (if available)

RISK METRICS:
{json.dumps(risk_metrics.__dict__ if risk_metrics else {}, indent=2)}

SEC FILINGS ANALYZED:
- Total filings: {len(sec_filings)}
- Filing types: {list(set(f.form_type for f in sec_filings))}
- Date range: {sec_filings[-1].filing_date if sec_filings else 'N/A'} to {sec_filings[0].filing_date if sec_filings else 'N/A'}

Provide comprehensive risk analysis including:

1. GOING CONCERN ANALYSIS:
   - Going concern warnings or qualifications
   - Financial distress indicators
   - Liquidity and solvency concerns

2. INTERNAL CONTROLS:
   - Material weaknesses in internal controls
   - Management remediation efforts
   - Auditor comments and concerns

3. EXECUTIVE CHANGES:
   - C-level executive turnover analysis
   - Reasons for departures and impact assessment
   - Board composition changes

4. LITIGATION AND REGULATORY:
   - Ongoing legal proceedings and investigations
   - Regulatory compliance issues
   - Potential financial impact assessment

5. ACCOUNTING QUALITY:
   - Non-GAAP adjustments and irregularities
   - Revenue recognition concerns
   - Impairment charges and write-offs

6. RELATED-PARTY TRANSACTIONS:
   - Transactions with insiders or affiliates
   - Potential conflicts of interest
   - Impact on financial statements

7. OVERALL RISK ASSESSMENT:
   - Risk score and prioritization
   - Key risk factors requiring monitoring
   - Mitigation strategies and outlook

Output all findings in structured JSON format with specific SEC filing citations."""

            # Get LLM analysis
            messages = [{"role": "user", "content": [{"text": analysis_prompt}]}]
            
            response, cost_info = await self.bedrock_client.converse_async(
                messages=messages,
                system_message=self.get_system_prompt(),
                max_tokens=3000,
                temperature=0.1
            )
            
            # Extract response text
            response_text = ""
            output_message = response.get('output', {}).get('message', {})
            content = output_message.get('content', [])
            
            for content_block in content:
                if 'text' in content_block:
                    response_text += content_block['text']
            
            # Extract key metrics
            key_metrics = {
                "risk_factors_identified": len(risk_metrics.going_concern_warnings) + len(risk_metrics.material_weaknesses) + len(risk_metrics.litigation_risks) if risk_metrics else 0,
                "executive_changes_count": len(risk_metrics.executive_changes) if risk_metrics else 0,
                "material_weaknesses_count": len(risk_metrics.material_weaknesses) if risk_metrics else 0,
                "litigation_issues_count": len(risk_metrics.litigation_risks) if risk_metrics else 0,
                "risk_score": risk_metrics.risk_score if risk_metrics else None,
                "filings_analyzed": len(sec_filings),
                "data_sources": ["yfinance", "SEC EDGAR"],
                "analysis_timestamp": datetime.now().isoformat()
            }
            
            # Generate recommendations
            recommendations = self._extract_risk_recommendations(risk_metrics)
            
            execution_time = (time.time() - start_time) * 1000
            
            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=response_text,
                confidence=0.88,
                key_metrics=key_metrics,
                recommendations=recommendations,
                timestamp=datetime.now().isoformat(),
                execution_time_ms=execution_time,
                cost=cost_info['total_cost']
            )
            
        except Exception as e:
            self.logger.logger.error(f"Risk monitoring analysis failed for {ticker}: {str(e)}")
            execution_time = (time.time() - start_time) * 1000
            
            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=f"Risk monitoring analysis failed: {str(e)}",
                confidence=0.0,
                key_metrics={},
                recommendations=[],
                timestamp=datetime.now().isoformat(),
                execution_time_ms=execution_time,
                cost=0.0
            )
    
    async def _calculate_risk_metrics(
        self, 
        ticker: str, 
        financial_metrics, 
        sec_filings
    ) -> Optional[RiskMetrics]:
        """Calculate risk metrics from SEC filings and financial data"""
        try:
            going_concern_warnings = []
            material_weaknesses = []
            executive_changes = []
            litigation_risks = []
            related_party_transactions = []
            accounting_irregularities = []
            impairment_charges = []
            
            # Analyze SEC filings for risk factors
            for filing in sec_filings:
                if filing.risk_factors:
                    # Categorize risk factors
                    for risk in filing.risk_factors:
                        risk_lower = risk.lower()
                        if any(keyword in risk_lower for keyword in ['going concern', 'substantial doubt', 'continue operations']):
                            going_concern_warnings.append(risk)
                        elif any(keyword in risk_lower for keyword in ['material weakness', 'internal control', 'sox']):
                            material_weaknesses.append(risk)
                        elif any(keyword in risk_lower for keyword in ['litigation', 'lawsuit', 'legal proceeding']):
                            litigation_risks.append(risk)
                        elif any(keyword in risk_lower for keyword in ['impairment', 'write-off', 'write-down']):
                            impairment_charges.append({"description": risk, "filing_date": filing.filing_date})
                
                # Check for executive changes in 8-K filings
                if filing.form_type == "8-K":
                    # This would need more sophisticated parsing in practice
                    if hasattr(filing, 'executive_changes') and filing.executive_changes:
                        executive_changes.append({
                            "filing_date": filing.filing_date,
                            "changes": "Executive change detected",
                            "form": "8-K"
                        })
            
            # Calculate risk score (simplified)
            risk_score = 0.0
            risk_score += len(going_concern_warnings) * 3.0  # High weight
            risk_score += len(material_weaknesses) * 2.0    # Medium weight
            risk_score += len(litigation_risks) * 1.5       # Medium weight
            risk_score += len(executive_changes) * 1.0      # Lower weight
            
            # Normalize to 0-10 scale
            risk_score = min(risk_score, 10.0)
            
            return RiskMetrics(
                ticker=ticker,
                going_concern_warnings=going_concern_warnings,
                material_weaknesses=material_weaknesses,
                executive_changes=executive_changes,
                litigation_risks=litigation_risks,
                related_party_transactions=related_party_transactions,
                accounting_irregularities=accounting_irregularities,
                impairment_charges=impairment_charges,
                risk_score=risk_score,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            self.logger.logger.error(f"Error calculating risk metrics: {str(e)}")
            return None
    
    def _extract_risk_recommendations(self, metrics: Optional[RiskMetrics]) -> List[str]:
        """Extract risk-based recommendations"""
        recommendations = []
        
        if not metrics:
            recommendations.append("Insufficient data for comprehensive risk analysis")
            return recommendations
        
        # Going concern recommendations
        if metrics.going_concern_warnings:
            recommendations.append("CRITICAL: Going concern warnings detected - assess financial viability")
        
        # Material weakness recommendations
        if metrics.material_weaknesses:
            recommendations.append("Material weaknesses in internal controls - monitor remediation progress")
        
        # Executive turnover recommendations
        if len(metrics.executive_changes) > 2:
            recommendations.append("High executive turnover - assess management stability")
        
        # Litigation recommendations
        if metrics.litigation_risks:
            recommendations.append("Active litigation risks - monitor potential financial impact")
        
        # Overall risk assessment
        if metrics.risk_score and metrics.risk_score > 7:
            recommendations.append("HIGH RISK: Multiple risk factors identified - exercise extreme caution")
        elif metrics.risk_score and metrics.risk_score > 4:
            recommendations.append("MODERATE RISK: Some risk factors present - monitor closely")
        else:
            recommendations.append("LOW RISK: Limited risk factors identified")
        
        if not recommendations:
            recommendations.append("Risk analysis requires more detailed SEC filing review")
        
        return recommendations


class ValuationAnalysisAgent(BaseAgent):
    """Agent specialized in valuation and financial health analysis"""

    def __init__(self, bedrock_client: BedrockClient, data_provider: FinancialDataProvider, config: Config):
        super().__init__("Valuation Analysis Specialist", bedrock_client, data_provider)
        self.sec_provider = EnhancedSECDataProvider(config)

    def get_system_prompt(self) -> str:
        return """You are a senior valuation analyst with expertise in financial health assessment and valuation modeling. You specialize in:

- Tangible book value per share calculations and analysis
- Insider ownership and trading activity analysis
- Share buyback activity and dilution trend monitoring
- Debt-to-equity ratios and leverage analysis
- ROIC/ROE trend analysis and value creation assessment
- Dividend policy changes and yield analysis
- Long-term revenue and earnings CAGR calculations

Your analysis must be:
- Calculate precise valuation metrics and ratios
- Analyze insider trading patterns and ownership changes
- Track capital allocation efficiency and shareholder returns
- Assess financial leverage and debt sustainability
- Monitor dividend policy and payout sustainability
- Calculate long-term growth rates and value creation metrics
- Output structured JSON format for all valuation metrics
- Provide fair value estimates and price targets

Always provide specific valuation metrics, growth rates, and financial health assessments."""

    async def analyze(self, ticker: str, context: Optional[Dict[str, Any]] = None) -> AgentResult:
        """Perform comprehensive valuation and financial health analysis"""
        import time
        start_time = time.time()

        try:
            self.logger.logger.info(f"Valuation Analysis Specialist analyzing {ticker}")

            # Get comprehensive financial data
            stock_data = await self.data_provider.get_stock_data(ticker)
            financial_metrics = await self.data_provider.get_financial_metrics(ticker)

            # Get SEC filings for detailed valuation analysis
            sec_filings = await self.sec_provider.get_multiple_filings(ticker, ["10-K", "10-Q", "DEF 14A"], 6)

            if not stock_data:
                raise ValueError(f"Unable to get stock data for {ticker}")

            # Calculate valuation metrics
            valuation_metrics = await self._calculate_valuation_metrics(ticker, financial_metrics, sec_filings)

            # Get historical data for CAGR calculations
            historical_data = await self._get_historical_valuation_data(ticker)

            # Prepare analysis prompt
            analysis_prompt = f"""Perform comprehensive valuation and financial health analysis for {ticker}:

CURRENT VALUATION:
- Current Price: ${stock_data.current_price:.2f}
- Market Cap: ${stock_data.market_cap/1e9:.1f}B (if available)
- P/E Ratio: {f'{stock_data.pe_ratio:.1f}' if stock_data.pe_ratio else 'N/A'}
- Dividend Yield: {f'{stock_data.dividend_yield*100:.2f}%' if stock_data.dividend_yield else 'N/A'}

VALUATION METRICS:
{json.dumps(valuation_metrics.__dict__ if valuation_metrics else {}, indent=2)}

HISTORICAL DATA:
{json.dumps(historical_data, indent=2)}

SEC FILINGS ANALYZED:
- Total filings: {len(sec_filings)}
- Proxy statements: {sum(1 for f in sec_filings if f.form_type == 'DEF 14A')}

Provide comprehensive valuation analysis including:

1. BOOK VALUE ANALYSIS:
   - Tangible book value per share calculation
   - Book value trends and asset quality assessment
   - Asset-based valuation considerations

2. INSIDER ACTIVITY:
   - Insider ownership percentage and changes
   - Recent insider trading activity analysis
   - Management alignment with shareholders

3. CAPITAL ALLOCATION:
   - Share buyback activity and effectiveness
   - Dilution trends from stock issuance
   - Capital allocation priorities assessment

4. FINANCIAL LEVERAGE:
   - Debt-to-equity analysis and trends
   - Interest coverage and debt sustainability
   - Optimal capital structure assessment

5. RETURN METRICS:
   - Return on invested capital (ROIC) trends
   - Return on equity (ROE) analysis
   - Value creation vs cost of capital

6. DIVIDEND ANALYSIS:
   - Dividend yield and payout ratio trends
   - Dividend policy changes and sustainability
   - Total shareholder return analysis

7. GROWTH ANALYSIS:
   - Long-term revenue CAGR (5-year)
   - Earnings CAGR and quality assessment
   - Sustainable growth rate calculation

8. VALUATION ASSESSMENT:
   - Fair value estimate and methodology
   - Relative valuation vs peers
   - Risk-adjusted return expectations

Output all metrics in structured JSON format with valuation methodology."""

            # Get LLM analysis
            messages = [{"role": "user", "content": [{"text": analysis_prompt}]}]

            response, cost_info = await self.bedrock_client.converse_async(
                messages=messages,
                system_message=self.get_system_prompt(),
                max_tokens=3000,
                temperature=0.1
            )

            # Extract response text
            response_text = ""
            output_message = response.get('output', {}).get('message', {})
            content = output_message.get('content', [])

            for content_block in content:
                if 'text' in content_block:
                    response_text += content_block['text']

            # Extract key metrics
            key_metrics = {
                "tangible_book_value_per_share": valuation_metrics.tangible_book_value_per_share if valuation_metrics else None,
                "debt_to_equity_ratio": valuation_metrics.debt_to_equity_ratio if valuation_metrics else None,
                "roe_current": valuation_metrics.roe_current if valuation_metrics else None,
                "roic_current": valuation_metrics.roic_current if valuation_metrics else None,
                "dividend_yield": valuation_metrics.dividend_yield_current if valuation_metrics else None,
                "revenue_cagr_5yr": valuation_metrics.revenue_cagr_5yr if valuation_metrics else None,
                "insider_ownership": valuation_metrics.insider_ownership_percent if valuation_metrics else None,
                "data_sources": ["yfinance", "SEC EDGAR"],
                "analysis_timestamp": datetime.now().isoformat()
            }

            # Generate recommendations
            recommendations = self._extract_valuation_recommendations(valuation_metrics, historical_data)

            execution_time = (time.time() - start_time) * 1000

            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=response_text,
                confidence=0.87,
                key_metrics=key_metrics,
                recommendations=recommendations,
                timestamp=datetime.now().isoformat(),
                execution_time_ms=execution_time,
                cost=cost_info['total_cost']
            )

        except Exception as e:
            self.logger.logger.error(f"Valuation analysis failed for {ticker}: {str(e)}")
            execution_time = (time.time() - start_time) * 1000

            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=f"Valuation analysis failed: {str(e)}",
                confidence=0.0,
                key_metrics={},
                recommendations=[],
                timestamp=datetime.now().isoformat(),
                execution_time_ms=execution_time,
                cost=0.0
            )
