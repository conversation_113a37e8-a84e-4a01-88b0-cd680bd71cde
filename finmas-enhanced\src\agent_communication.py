"""
Dynamic Agent Communication System

This module implements dynamic agent communication where agents can
request information from other agents based on their analysis needs.
"""

import asyncio
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum

from .agents import AgentResult, BaseAgent
from utils.logging_utils import get_logger


class CommunicationPriority(Enum):
    """Priority levels for agent communication requests"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class CommunicationRequest:
    """Represents a request from one agent to another"""
    requesting_agent: str
    target_agent: str
    request_type: str
    priority: CommunicationPriority
    context: Dict[str, Any]
    ticker: str
    
    def __post_init__(self):
        self.timestamp = asyncio.get_event_loop().time()


class AgentCommunicationHub:
    """Central hub for managing agent-to-agent communication"""
    
    def __init__(self, agents: Dict[str, BaseAgent]):
        self.agents = agents
        self.logger = get_logger()
        self.communication_log: List[CommunicationRequest] = []
        self.agent_dependencies = self._build_dependency_graph()
        
    def _build_dependency_graph(self) -> Dict[str, Set[str]]:
        """Build dependency graph showing which agents typically need data from others"""
        return {
            "investment": {"technical", "fundamental", "news", "market", "risk"},
            "risk": {"technical", "fundamental", "market"},
            "market": {"technical", "fundamental"},
            "news": set(),  # News analysis is independent
            "technical": set(),  # Technical analysis is independent
            "fundamental": set()  # Fundamental analysis is independent
        }
    
    async def analyze_with_communication(
        self, 
        ticker: str, 
        primary_agent: str,
        user_query: str,
        max_communication_rounds: int = 3
    ) -> Dict[str, AgentResult]:
        """
        Perform analysis with dynamic agent communication
        
        Args:
            ticker: Stock ticker to analyze
            primary_agent: The main agent handling the query
            user_query: Original user query for context
            max_communication_rounds: Maximum rounds of inter-agent communication
            
        Returns:
            Dictionary of agent results
        """
        self.logger.logger.info(f"Starting dynamic analysis for {ticker} with primary agent: {primary_agent}")
        
        results = {}
        communication_round = 0
        
        # Start with the primary agent
        if primary_agent in self.agents:
            try:
                result = await self.agents[primary_agent].analyze(ticker)
                results[primary_agent] = result
                self.logger.logger.info(f"Primary agent {primary_agent} completed analysis")
            except Exception as e:
                self.logger.logger.error(f"Primary agent {primary_agent} failed: {str(e)}")
                return results
        
        # Dynamic communication rounds
        while communication_round < max_communication_rounds:
            communication_round += 1
            self.logger.logger.info(f"Communication round {communication_round}")
            
            # Determine what additional analysis is needed
            needed_agents = await self._determine_needed_agents(
                ticker, user_query, results, primary_agent
            )
            
            if not needed_agents:
                self.logger.logger.info("No additional agents needed")
                break
            
            # Execute needed agents in parallel
            new_results = await self._execute_agents_parallel(ticker, needed_agents, results)
            results.update(new_results)
            
            # Check if we have enough information
            if await self._is_analysis_complete(user_query, results):
                self.logger.logger.info("Analysis deemed complete")
                break
        
        self.logger.logger.info(f"Dynamic analysis complete with {len(results)} agents")
        return results
    
    async def _determine_needed_agents(
        self, 
        ticker: str, 
        user_query: str, 
        current_results: Dict[str, AgentResult],
        primary_agent: str
    ) -> List[str]:
        """
        Use LLM to determine what additional agents are needed
        """
        # Get the primary agent's result for context
        primary_result = current_results.get(primary_agent)
        if not primary_result:
            return []
        
        # Prepare context for LLM decision
        analysis_context = f"""
Current analysis for {ticker}:
Primary Agent: {primary_agent}
Primary Analysis: {primary_result.analysis[:500]}...
Confidence: {primary_result.confidence}

User Query: {user_query}

Available agents not yet used: {[agent for agent in self.agents.keys() if agent not in current_results]}
"""
        
        # Use function calling to determine needed agents
        from .bedrock_client import BedrockClient
        
        # For now, use dependency-based logic (can be enhanced with LLM later)
        needed_agents = []
        
        # Check dependencies
        if primary_agent in self.agent_dependencies:
            for dep_agent in self.agent_dependencies[primary_agent]:
                if dep_agent not in current_results and dep_agent in self.agents:
                    needed_agents.append(dep_agent)
        
        # Add investment advisor if we have multiple analyses but no synthesis
        if len(current_results) > 1 and "investment" not in current_results and "investment" in self.agents:
            needed_agents.append("investment")
        
        # Limit to 2 agents per round to avoid overwhelming
        return needed_agents[:2]
    
    async def _execute_agents_parallel(
        self, 
        ticker: str, 
        agent_names: List[str], 
        context_results: Dict[str, AgentResult]
    ) -> Dict[str, AgentResult]:
        """Execute multiple agents in parallel with context"""
        tasks = []
        
        for agent_name in agent_names:
            if agent_name in self.agents:
                # Pass context from previous agents
                tasks.append(self.agents[agent_name].analyze(ticker, context_results))
        
        if not tasks:
            return {}
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        agent_results = {}
        for i, result in enumerate(results):
            agent_name = agent_names[i]
            if isinstance(result, AgentResult):
                agent_results[agent_name] = result
                self.logger.logger.info(f"Agent {agent_name} completed successfully")
            else:
                self.logger.logger.error(f"Agent {agent_name} failed: {str(result)}")
        
        return agent_results
    
    async def _is_analysis_complete(self, user_query: str, results: Dict[str, AgentResult]) -> bool:
        """
        Determine if the current analysis is sufficient to answer the user query
        """
        # Simple heuristics for now (can be enhanced with LLM)
        
        # If we have investment advisor result, analysis is likely complete
        if "investment" in results:
            return True
        
        # If we have at least 3 different types of analysis
        if len(results) >= 3:
            return True
        
        # Check query type
        query_lower = user_query.lower()
        
        # For comparison queries, need at least technical and fundamental
        if any(word in query_lower for word in ['compare', 'vs', 'versus']):
            return "technical" in results and "fundamental" in results
        
        # For recommendation queries, need comprehensive analysis
        if any(word in query_lower for word in ['recommend', 'should i', 'invest']):
            return len(results) >= 3
        
        # Default: need at least 2 analyses
        return len(results) >= 2
    
    def get_communication_summary(self) -> Dict[str, Any]:
        """Get summary of agent communications"""
        return {
            "total_requests": len(self.communication_log),
            "agents_involved": len(set(req.requesting_agent for req in self.communication_log) | 
                                 set(req.target_agent for req in self.communication_log)),
            "communication_patterns": self._analyze_communication_patterns()
        }
    
    def _analyze_communication_patterns(self) -> Dict[str, Any]:
        """Analyze patterns in agent communication"""
        if not self.communication_log:
            return {}
        
        patterns = {}
        for req in self.communication_log:
            key = f"{req.requesting_agent}->{req.target_agent}"
            patterns[key] = patterns.get(key, 0) + 1
        
        return patterns


class SmartAgentOrchestrator:
    """Smart orchestrator that uses dynamic communication"""
    
    def __init__(self, agents: Dict[str, BaseAgent]):
        self.communication_hub = AgentCommunicationHub(agents)
        self.logger = get_logger()
    
    async def analyze_query(
        self, 
        ticker: str, 
        user_query: str, 
        query_intent: Any
    ) -> Dict[str, AgentResult]:
        """
        Analyze query using smart agent orchestration
        
        Args:
            ticker: Stock ticker
            user_query: Original user query
            query_intent: Parsed query intent
            
        Returns:
            Dictionary of agent results
        """
        # Determine primary agent based on intent
        primary_agent = self._determine_primary_agent(query_intent)
        
        self.logger.logger.info(f"Smart orchestration starting with primary agent: {primary_agent}")
        
        # Use dynamic communication for analysis
        results = await self.communication_hub.analyze_with_communication(
            ticker=ticker,
            primary_agent=primary_agent,
            user_query=user_query,
            max_communication_rounds=3
        )
        
        return results
    
    def _determine_primary_agent(self, query_intent: Any) -> str:
        """Determine which agent should lead the analysis"""
        intent_to_agent = {
            "technical": "technical",
            "fundamental": "fundamental", 
            "news": "news",
            "recommendation": "investment",
            "compare": "market",  # Market analyst good for comparisons
            "analyze": "technical"  # Default to technical for general analysis
        }
        
        return intent_to_agent.get(query_intent.primary_intent, "technical")
