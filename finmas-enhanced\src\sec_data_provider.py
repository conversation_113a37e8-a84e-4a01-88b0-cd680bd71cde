"""
Enhanced SEC Data Provider using edgartools

This module provides comprehensive SEC filing data extraction and analysis
using the edgartools library for real-time SEC filing access.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
import re
from pathlib import Path

# Import edgartools components
from edgar import Company, Filing, set_identity
from edgar.entities import EntityFilings

from config import Config
from utils.logging_utils import get_logger


# Set identity for SEC API access
set_identity("FinMAS Enhanced Financial <NAME_EMAIL>")


@dataclass
class SECFilingData:
    """Represents SEC filing data with extracted metrics"""
    ticker: str
    form_type: str
    filing_date: str
    accession_number: str
    period_end_date: Optional[str]
    fiscal_year: Optional[int]
    fiscal_quarter: Optional[int]
    revenue: Optional[float]
    net_income: Optional[float]
    total_assets: Optional[float]
    total_liabilities: Optional[float]
    shareholders_equity: Optional[float]
    cash_and_equivalents: Optional[float]
    operating_cash_flow: Optional[float]
    free_cash_flow: Optional[float]
    research_development: Optional[float]
    selling_general_admin: Optional[float]
    cost_of_revenue: Optional[float]
    segment_data: Optional[Dict[str, Any]]
    risk_factors: Optional[List[str]]
    management_discussion: Optional[str]
    timestamp: str


@dataclass
class FinancialSegment:
    """Represents business segment financial data"""
    segment_name: str
    revenue: Optional[float]
    operating_income: Optional[float]
    assets: Optional[float]
    geographic_region: Optional[str]
    growth_rate: Optional[float]


@dataclass
class ExecutiveChange:
    """Represents executive leadership changes"""
    name: str
    position: str
    change_type: str  # "appointment", "resignation", "termination"
    effective_date: str
    reason: Optional[str]


class EnhancedSECDataProvider:
    """Enhanced SEC data provider with comprehensive filing analysis"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger()
        
        # Supported filing types for comprehensive analysis
        self.supported_forms = ["10-K", "10-Q", "8-K", "DEF 14A", "20-F"]
        
        self.logger.logger.info("Enhanced SEC data provider initialized")
    
    async def get_latest_filing(self, ticker: str, form_type: str = "10-K") -> Optional[SECFilingData]:
        """Get the latest SEC filing for a company"""
        try:
            self.logger.logger.info(f"Fetching latest {form_type} filing for {ticker}")
            
            company = Company(ticker)
            filings = company.get_filings(form=form_type).latest(1)
            
            if not filings:
                self.logger.logger.warning(f"No {form_type} filings found for {ticker}")
                return None
            
            filing = filings[0]
            return await self._extract_filing_data(ticker, filing)
            
        except Exception as e:
            self.logger.logger.error(f"Failed to get {form_type} filing for {ticker}: {str(e)}")
            return None
    
    async def get_multiple_filings(
        self, 
        ticker: str, 
        form_types: List[str] = None, 
        count: int = 4
    ) -> List[SECFilingData]:
        """Get multiple recent filings for comprehensive analysis"""
        if form_types is None:
            form_types = ["10-K", "10-Q"]
        
        all_filings = []
        
        try:
            company = Company(ticker)
            
            for form_type in form_types:
                self.logger.logger.info(f"Fetching {count} {form_type} filings for {ticker}")
                
                filings = company.get_filings(form=form_type).latest(count)
                
                for filing in filings:
                    filing_data = await self._extract_filing_data(ticker, filing)
                    if filing_data:
                        all_filings.append(filing_data)
            
            # Sort by filing date (most recent first)
            all_filings.sort(key=lambda x: x.filing_date, reverse=True)
            
            self.logger.logger.info(f"Retrieved {len(all_filings)} filings for {ticker}")
            return all_filings
            
        except Exception as e:
            self.logger.logger.error(f"Failed to get multiple filings for {ticker}: {str(e)}")
            return []
    
    async def _extract_filing_data(self, ticker: str, filing: Filing) -> Optional[SECFilingData]:
        """Extract structured data from SEC filing"""
        try:
            # Get basic filing information
            filing_info = {
                "ticker": ticker,
                "form_type": filing.form,
                "filing_date": filing.filing_date.isoformat() if filing.filing_date else "",
                "accession_number": filing.accession_number,
                "timestamp": datetime.now().isoformat()
            }
            
            # Extract financial data based on form type
            if filing.form in ["10-K", "10-Q"]:
                financial_data = await self._extract_financial_statements(filing)
                filing_info.update(financial_data)
                
                # Extract segment data
                segment_data = await self._extract_segment_data(filing)
                filing_info["segment_data"] = segment_data
                
                # Extract risk factors and MD&A
                risk_factors = await self._extract_risk_factors(filing)
                filing_info["risk_factors"] = risk_factors
                
                mda = await self._extract_management_discussion(filing)
                filing_info["management_discussion"] = mda
                
            elif filing.form == "8-K":
                # Extract material events and corporate changes
                events_data = await self._extract_8k_events(filing)
                filing_info.update(events_data)
                
            elif filing.form == "DEF 14A":
                # Extract executive compensation and governance data
                governance_data = await self._extract_proxy_data(filing)
                filing_info.update(governance_data)
            
            return SECFilingData(**filing_info)
            
        except Exception as e:
            self.logger.logger.error(f"Failed to extract data from filing: {str(e)}")
            return None
    
    async def _extract_financial_statements(self, filing: Filing) -> Dict[str, Any]:
        """Extract financial statement data from 10-K/10-Q filings"""
        financial_data = {}
        
        try:
            # This is a simplified extraction - in practice, you'd use XBRL parsing
            # or more sophisticated text extraction methods
            
            # Get the filing content
            filing_text = filing.text()
            
            # Extract key financial metrics using pattern matching
            # Note: This is a basic implementation - production would use XBRL data
            
            # Revenue patterns
            revenue_patterns = [
                r"Total\s+(?:net\s+)?revenues?\s*[\$\s]*([0-9,]+)",
                r"Net\s+sales\s*[\$\s]*([0-9,]+)",
                r"Total\s+revenue\s*[\$\s]*([0-9,]+)"
            ]
            
            for pattern in revenue_patterns:
                match = re.search(pattern, filing_text, re.IGNORECASE)
                if match:
                    financial_data["revenue"] = self._parse_financial_number(match.group(1))
                    break
            
            # Net income patterns
            income_patterns = [
                r"Net\s+income\s*[\$\s]*([0-9,]+)",
                r"Net\s+earnings\s*[\$\s]*([0-9,]+)"
            ]
            
            for pattern in income_patterns:
                match = re.search(pattern, filing_text, re.IGNORECASE)
                if match:
                    financial_data["net_income"] = self._parse_financial_number(match.group(1))
                    break
            
            # Extract period information
            period_match = re.search(r"period\s+ended?\s+([A-Za-z]+\s+\d{1,2},?\s+\d{4})", filing_text, re.IGNORECASE)
            if period_match:
                financial_data["period_end_date"] = period_match.group(1)
            
        except Exception as e:
            self.logger.logger.error(f"Error extracting financial statements: {str(e)}")
        
        return financial_data
    
    async def _extract_segment_data(self, filing: Filing) -> Dict[str, Any]:
        """Extract business segment data"""
        segment_data = {}
        
        try:
            filing_text = filing.text()
            
            # Look for segment reporting sections
            segment_section = re.search(
                r"segment\s+(?:information|reporting|results)(.*?)(?:note\s+\d+|consolidated|total)",
                filing_text,
                re.IGNORECASE | re.DOTALL
            )
            
            if segment_section:
                segment_text = segment_section.group(1)
                
                # Extract segment names and revenue (simplified)
                segments = re.findall(
                    r"([A-Za-z\s&]+)\s+segment.*?revenue.*?\$([0-9,]+)",
                    segment_text,
                    re.IGNORECASE
                )
                
                segment_data["segments"] = []
                for segment_name, revenue in segments:
                    segment_data["segments"].append({
                        "name": segment_name.strip(),
                        "revenue": self._parse_financial_number(revenue)
                    })
        
        except Exception as e:
            self.logger.logger.error(f"Error extracting segment data: {str(e)}")
        
        return segment_data
    
    async def _extract_risk_factors(self, filing: Filing) -> List[str]:
        """Extract risk factors from filing"""
        risk_factors = []
        
        try:
            filing_text = filing.text()
            
            # Find risk factors section
            risk_section = re.search(
                r"risk\s+factors(.*?)(?:item\s+\d+|unresolved|properties)",
                filing_text,
                re.IGNORECASE | re.DOTALL
            )
            
            if risk_section:
                risk_text = risk_section.group(1)
                
                # Extract individual risk factors (simplified)
                risks = re.findall(
                    r"(?:•|\*|-)?\s*([A-Z][^.]*(?:risk|uncertainty|challenge)[^.]*\.)",
                    risk_text,
                    re.IGNORECASE
                )
                
                risk_factors = [risk.strip() for risk in risks[:10]]  # Limit to top 10
        
        except Exception as e:
            self.logger.logger.error(f"Error extracting risk factors: {str(e)}")
        
        return risk_factors
    
    async def _extract_management_discussion(self, filing: Filing) -> Optional[str]:
        """Extract Management Discussion and Analysis section"""
        try:
            filing_text = filing.text()
            
            # Find MD&A section
            mda_section = re.search(
                r"management.?s\s+discussion\s+and\s+analysis(.*?)(?:item\s+\d+|quantitative|controls)",
                filing_text,
                re.IGNORECASE | re.DOTALL
            )
            
            if mda_section:
                mda_text = mda_section.group(1)
                # Return first 2000 characters for summary
                return mda_text[:2000].strip()
        
        except Exception as e:
            self.logger.logger.error(f"Error extracting MD&A: {str(e)}")
        
        return None
    
    async def _extract_8k_events(self, filing: Filing) -> Dict[str, Any]:
        """Extract material events from 8-K filings"""
        events_data = {}
        
        try:
            filing_text = filing.text()
            
            # Look for common 8-K items
            items = {
                "executive_changes": r"item\s+5\.02.*?departure.*?director.*?officer",
                "material_agreements": r"item\s+1\.01.*?material.*?agreement",
                "acquisitions": r"item\s+2\.01.*?completion.*?acquisition",
                "earnings_guidance": r"item\s+2\.02.*?results.*?operations"
            }
            
            for event_type, pattern in items.items():
                if re.search(pattern, filing_text, re.IGNORECASE | re.DOTALL):
                    events_data[event_type] = True
        
        except Exception as e:
            self.logger.logger.error(f"Error extracting 8-K events: {str(e)}")
        
        return events_data
    
    async def _extract_proxy_data(self, filing: Filing) -> Dict[str, Any]:
        """Extract executive compensation and governance data from proxy statements"""
        proxy_data = {}
        
        try:
            filing_text = filing.text()
            
            # Extract executive compensation summary (simplified)
            comp_section = re.search(
                r"summary\s+compensation\s+table(.*?)(?:grants|outstanding)",
                filing_text,
                re.IGNORECASE | re.DOTALL
            )
            
            if comp_section:
                proxy_data["has_compensation_data"] = True
        
        except Exception as e:
            self.logger.logger.error(f"Error extracting proxy data: {str(e)}")
        
        return proxy_data
    
    def _parse_financial_number(self, number_str: str) -> Optional[float]:
        """Parse financial number from string (handles millions, billions)"""
        try:
            # Remove commas and convert to float
            clean_number = re.sub(r'[,$]', '', number_str.strip())
            
            # Handle millions/billions notation
            if 'million' in number_str.lower() or 'mil' in number_str.lower():
                return float(clean_number) * 1_000_000
            elif 'billion' in number_str.lower() or 'bil' in number_str.lower():
                return float(clean_number) * 1_000_000_000
            else:
                return float(clean_number)
        
        except (ValueError, AttributeError):
            return None
    
    async def get_filing_summary(self, ticker: str, form_type: str = "10-K") -> Dict[str, Any]:
        """Get a structured summary of the latest filing"""
        filing_data = await self.get_latest_filing(ticker, form_type)
        
        if not filing_data:
            return {"error": f"No {form_type} filing found for {ticker}"}
        
        return {
            "ticker": filing_data.ticker,
            "form_type": filing_data.form_type,
            "filing_date": filing_data.filing_date,
            "period_end_date": filing_data.period_end_date,
            "financial_highlights": {
                "revenue": filing_data.revenue,
                "net_income": filing_data.net_income,
                "total_assets": filing_data.total_assets,
                "free_cash_flow": filing_data.free_cash_flow
            },
            "segment_count": len(filing_data.segment_data.get("segments", [])) if filing_data.segment_data else 0,
            "risk_factors_count": len(filing_data.risk_factors) if filing_data.risk_factors else 0,
            "has_management_discussion": bool(filing_data.management_discussion),
            "data_source": "SEC EDGAR via edgartools",
            "timestamp": filing_data.timestamp
        }
