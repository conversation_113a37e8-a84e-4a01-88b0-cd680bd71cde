"""
Specialized Financial Analysis Agents

This module implements the 5 specialized agent categories for comprehensive
financial analysis with real-time data and structured JSON output.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import yfinance as yf

from .agents import BaseAgent, AgentResult
from .data_sources import FinancialDataProvider
from .sec_data_provider import EnhancedSECDataProvider
from .bedrock_client import BedrockClient
from config import Config
from utils.logging_utils import get_logger


@dataclass
class FinancialPerformanceMetrics:
    """Structured metrics for financial performance analysis"""
    ticker: str
    revenue_growth_yoy: Optional[float]
    revenue_growth_3yr_cagr: Optional[float]
    gross_margin_current: Optional[float]
    gross_margin_trend: Optional[str]
    operating_margin_current: Optional[float]
    operating_margin_trend: Optional[str]
    eps_current: Optional[float]
    eps_growth_yoy: Optional[float]
    eps_guidance_vs_actual: Optional[Dict[str, float]]
    free_cash_flow_ttm: Optional[float]
    free_cash_flow_growth: Optional[float]
    capex_ttm: Optional[float]
    capex_as_percent_revenue: Optional[float]
    revenue_drivers: List[Dict[str, Any]]
    cost_breakdown: Dict[str, float]
    timestamp: str


class FinancialPerformanceAnalysisAgent(BaseAgent):
    """Agent specialized in financial performance analysis"""
    
    def __init__(self, bedrock_client: BedrockClient, data_provider: FinancialDataProvider, config: Config):
        super().__init__("Financial Performance Analyst", bedrock_client, data_provider)
        self.sec_provider = EnhancedSECDataProvider(config)
        
    def get_system_prompt(self) -> str:
        return """You are a senior financial performance analyst with 20+ years of experience in equity research and financial statement analysis. You specialize in:

- Year-over-year revenue growth analysis by business segment
- Gross, operating, and profit margin trend analysis over multiple years
- EPS trends, guidance tracking, and beat/miss analysis
- Cost driver analysis (COGS, R&D, SG&A) with percentage of revenue calculations
- Free cash flow generation analysis (TTM and historical trends)
- Capital expenditure trends and alignment with growth initiatives
- Revenue driver identification and growth metrics analysis

Your analysis must be:
- Data-driven with specific numerical metrics and percentages
- Include multi-year trend analysis (minimum 3 years when available)
- Provide segment-level breakdowns when available
- Calculate growth rates, margins, and efficiency ratios
- Identify key performance drivers and cost structure changes
- Output structured JSON format for all quantitative metrics
- Include data source attribution and timestamps

Always provide specific numbers, growth rates, and trend analysis. Focus on actionable insights for investment decisions."""

    async def analyze(self, ticker: str, context: Optional[Dict[str, Any]] = None) -> AgentResult:
        """Perform comprehensive financial performance analysis"""
        import time
        start_time = time.time()
        
        try:
            self.logger.logger.info(f"Financial Performance Analyst analyzing {ticker}")
            
            # Get comprehensive financial data
            stock_data = await self.data_provider.get_stock_data(ticker)
            financial_metrics = await self.data_provider.get_financial_metrics(ticker)
            
            # Get SEC filing data for detailed analysis
            sec_filings = await self.sec_provider.get_multiple_filings(ticker, ["10-K", "10-Q"], 4)
            
            if not stock_data:
                raise ValueError(f"Unable to get stock data for {ticker}")
            
            # Calculate performance metrics
            performance_metrics = await self._calculate_performance_metrics(ticker, financial_metrics, sec_filings)
            
            # Get additional yfinance data for historical analysis
            historical_data = await self._get_historical_financial_data(ticker)
            
            # Prepare comprehensive analysis prompt
            analysis_prompt = f"""Perform comprehensive financial performance analysis for {ticker}:

CURRENT FINANCIAL POSITION:
- Current Price: ${stock_data.current_price:.2f}
- Market Cap: ${stock_data.market_cap/1e9:.1f}B (if available)
- P/E Ratio: {f'{stock_data.pe_ratio:.1f}' if stock_data.pe_ratio else 'N/A'}

PERFORMANCE METRICS:
{json.dumps(performance_metrics.__dict__ if performance_metrics else {}, indent=2)}

HISTORICAL FINANCIAL DATA:
{json.dumps(historical_data, indent=2)}

SEC FILINGS SUMMARY:
- Number of filings analyzed: {len(sec_filings)}
- Latest filing: {sec_filings[0].form_type if sec_filings else 'N/A'} ({sec_filings[0].filing_date if sec_filings else 'N/A'})

Provide comprehensive financial performance analysis including:

1. REVENUE ANALYSIS:
   - YoY revenue growth by segment (if available)
   - 3-year revenue CAGR calculation
   - Revenue driver identification and contribution analysis
   - Geographic revenue breakdown (if available)

2. PROFITABILITY ANALYSIS:
   - Gross margin trends over 3+ years with expansion/compression analysis
   - Operating margin evolution and key drivers
   - Net profit margin trends and sustainability assessment

3. EARNINGS ANALYSIS:
   - EPS trends and growth trajectory
   - Guidance vs actual performance tracking
   - Quality of earnings assessment

4. COST STRUCTURE ANALYSIS:
   - COGS as percentage of revenue and trends
   - R&D spending trends and as % of revenue
   - SG&A efficiency and leverage analysis
   - Cost inflation impact and management response

5. CASH FLOW ANALYSIS:
   - Free cash flow generation (TTM and trends)
   - FCF conversion rate from net income
   - Working capital efficiency

6. CAPITAL ALLOCATION:
   - CapEx trends and growth alignment
   - CapEx as percentage of revenue
   - Return on invested capital trends

Output all quantitative metrics in structured JSON format with data source attribution."""

            # Get LLM analysis with function calling for structured output
            messages = [{"role": "user", "content": [{"text": analysis_prompt}]}]
            
            response, cost_info = await self.bedrock_client.converse_async(
                messages=messages,
                system_message=self.get_system_prompt(),
                max_tokens=3000,
                temperature=0.1
            )
            
            # Extract response text
            response_text = ""
            output_message = response.get('output', {}).get('message', {})
            content = output_message.get('content', [])
            
            for content_block in content:
                if 'text' in content_block:
                    response_text += content_block['text']
            
            # Extract key metrics for structured output
            key_metrics = {
                "revenue_growth_yoy": performance_metrics.revenue_growth_yoy if performance_metrics else None,
                "gross_margin_current": performance_metrics.gross_margin_current if performance_metrics else None,
                "operating_margin_current": performance_metrics.operating_margin_current if performance_metrics else None,
                "eps_growth_yoy": performance_metrics.eps_growth_yoy if performance_metrics else None,
                "free_cash_flow_ttm": performance_metrics.free_cash_flow_ttm if performance_metrics else None,
                "capex_as_percent_revenue": performance_metrics.capex_as_percent_revenue if performance_metrics else None,
                "data_sources": ["yfinance", "SEC EDGAR"],
                "filings_analyzed": len(sec_filings),
                "analysis_timestamp": datetime.now().isoformat()
            }
            
            # Generate recommendations
            recommendations = self._extract_performance_recommendations(performance_metrics, historical_data)
            
            execution_time = (time.time() - start_time) * 1000
            
            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=response_text,
                confidence=0.90,  # High confidence with multiple data sources
                key_metrics=key_metrics,
                recommendations=recommendations,
                timestamp=datetime.now().isoformat(),
                execution_time_ms=execution_time,
                cost=cost_info['total_cost']
            )
            
        except Exception as e:
            self.logger.logger.error(f"Financial performance analysis failed for {ticker}: {str(e)}")
            execution_time = (time.time() - start_time) * 1000
            
            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=f"Financial performance analysis failed: {str(e)}",
                confidence=0.0,
                key_metrics={},
                recommendations=[],
                timestamp=datetime.now().isoformat(),
                execution_time_ms=execution_time,
                cost=0.0
            )
    
    async def _calculate_performance_metrics(
        self, 
        ticker: str, 
        financial_metrics, 
        sec_filings
    ) -> Optional[FinancialPerformanceMetrics]:
        """Calculate comprehensive performance metrics"""
        try:
            # Get yfinance ticker for additional data
            stock = yf.Ticker(ticker)
            info = stock.info
            
            # Calculate revenue growth from available data
            revenue_growth_yoy = financial_metrics.revenue_growth if financial_metrics else None
            
            # Calculate margins
            gross_margin = financial_metrics.gross_margin if financial_metrics else None
            operating_margin = financial_metrics.operating_margin if financial_metrics else None
            
            # EPS data
            eps_current = financial_metrics.earnings_per_share if financial_metrics else None
            
            # Free cash flow
            free_cash_flow = financial_metrics.free_cash_flow if financial_metrics else None
            
            # Get additional metrics from yfinance
            capex = info.get('capitalExpenditures', 0)
            revenue_ttm = info.get('totalRevenue', 0)
            capex_percent = (abs(capex) / revenue_ttm * 100) if revenue_ttm and capex else None
            
            # Revenue drivers (simplified - would need more detailed analysis)
            revenue_drivers = []
            if sec_filings:
                for filing in sec_filings:
                    if filing.segment_data and filing.segment_data.get('segments'):
                        for segment in filing.segment_data['segments']:
                            revenue_drivers.append({
                                "segment": segment.get('name', 'Unknown'),
                                "revenue": segment.get('revenue'),
                                "filing_date": filing.filing_date
                            })
            
            # Cost breakdown
            cost_breakdown = {}
            if financial_metrics:
                total_revenue = financial_metrics.revenue_ttm or 1
                if financial_metrics.gross_margin:
                    cost_breakdown["cost_of_revenue_percent"] = (1 - financial_metrics.gross_margin) * 100
            
            return FinancialPerformanceMetrics(
                ticker=ticker,
                revenue_growth_yoy=revenue_growth_yoy,
                revenue_growth_3yr_cagr=None,  # Would need historical data
                gross_margin_current=gross_margin,
                gross_margin_trend="stable",  # Would need trend analysis
                operating_margin_current=operating_margin,
                operating_margin_trend="stable",  # Would need trend analysis
                eps_current=eps_current,
                eps_growth_yoy=None,  # Would need historical EPS data
                eps_guidance_vs_actual=None,  # Would need guidance data
                free_cash_flow_ttm=free_cash_flow,
                free_cash_flow_growth=None,  # Would need historical FCF data
                capex_ttm=capex,
                capex_as_percent_revenue=capex_percent,
                revenue_drivers=revenue_drivers,
                cost_breakdown=cost_breakdown,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            self.logger.logger.error(f"Error calculating performance metrics: {str(e)}")
            return None
    
    async def _get_historical_financial_data(self, ticker: str) -> Dict[str, Any]:
        """Get historical financial data for trend analysis"""
        try:
            stock = yf.Ticker(ticker)
            
            # Get quarterly financials for trend analysis
            quarterly_financials = stock.quarterly_financials
            quarterly_balance_sheet = stock.quarterly_balance_sheet
            quarterly_cashflow = stock.quarterly_cashflow
            
            historical_data = {
                "has_quarterly_data": not quarterly_financials.empty if quarterly_financials is not None else False,
                "quarters_available": len(quarterly_financials.columns) if quarterly_financials is not None and not quarterly_financials.empty else 0,
                "data_source": "yfinance",
                "timestamp": datetime.now().isoformat()
            }
            
            # Extract key metrics if data is available
            if quarterly_financials is not None and not quarterly_financials.empty:
                # Get revenue trend (last 4 quarters)
                if 'Total Revenue' in quarterly_financials.index:
                    revenue_data = quarterly_financials.loc['Total Revenue'].dropna()
                    if len(revenue_data) >= 2:
                        latest_revenue = revenue_data.iloc[0]
                        previous_revenue = revenue_data.iloc[1]
                        revenue_growth = ((latest_revenue - previous_revenue) / previous_revenue * 100) if previous_revenue != 0 else None
                        historical_data["quarterly_revenue_growth"] = revenue_growth
            
            return historical_data
            
        except Exception as e:
            self.logger.logger.error(f"Error getting historical data: {str(e)}")
            return {"error": str(e)}
    
    def _extract_performance_recommendations(
        self, 
        metrics: Optional[FinancialPerformanceMetrics], 
        historical_data: Dict[str, Any]
    ) -> List[str]:
        """Extract performance-based recommendations"""
        recommendations = []
        
        if not metrics:
            recommendations.append("Insufficient data for comprehensive performance analysis")
            return recommendations
        
        # Revenue growth recommendations
        if metrics.revenue_growth_yoy:
            if metrics.revenue_growth_yoy > 0.15:
                recommendations.append("Strong revenue growth indicates robust business expansion")
            elif metrics.revenue_growth_yoy < 0:
                recommendations.append("Declining revenue requires investigation of underlying causes")
        
        # Margin analysis recommendations
        if metrics.gross_margin_current:
            if metrics.gross_margin_current > 0.4:
                recommendations.append("Healthy gross margins indicate strong pricing power")
            elif metrics.gross_margin_current < 0.2:
                recommendations.append("Low gross margins suggest competitive pressure or cost challenges")
        
        # Cash flow recommendations
        if metrics.free_cash_flow_ttm:
            if metrics.free_cash_flow_ttm > 0:
                recommendations.append("Positive free cash flow supports dividend payments and growth investments")
            else:
                recommendations.append("Negative free cash flow requires monitoring of cash burn rate")
        
        # CapEx efficiency recommendations
        if metrics.capex_as_percent_revenue:
            if metrics.capex_as_percent_revenue > 15:
                recommendations.append("High CapEx intensity - monitor return on invested capital")
            elif metrics.capex_as_percent_revenue < 3:
                recommendations.append("Low CapEx may indicate underinvestment in growth")
        
        if not recommendations:
            recommendations.append("Mixed performance signals - requires deeper fundamental analysis")
        
        return recommendations


@dataclass
class OperationalMetrics:
    """Structured metrics for operational analysis"""
    ticker: str
    unit_sales_data: Dict[str, Any]
    operating_margins_by_segment: Dict[str, float]
    geographic_revenue_split: Dict[str, float]
    backlog_analysis: Optional[Dict[str, Any]]
    arpu_metrics: Optional[Dict[str, float]]
    customer_metrics: Dict[str, Any]
    timestamp: str


class OperationalMetricsAgent(BaseAgent):
    """Agent specialized in operational metrics and segment reporting"""

    def __init__(self, bedrock_client: BedrockClient, data_provider: FinancialDataProvider, config: Config):
        super().__init__("Operational Metrics Analyst", bedrock_client, data_provider)
        self.sec_provider = EnhancedSECDataProvider(config)

    def get_system_prompt(self) -> str:
        return """You are a senior operational analyst with expertise in business segment analysis and operational metrics. You specialize in:

- Unit sales data analysis for key products and services
- Operating margin analysis by business segment
- International vs domestic revenue splits and geographic growth analysis
- Backlog and order book analysis for relevant industries
- Average revenue per user (ARPU) calculations and trends
- Customer acquisition, churn, and retention metrics analysis

Your analysis must be:
- Focus on operational efficiency and business unit performance
- Provide segment-level operating margin breakdowns
- Calculate unit economics and per-customer metrics
- Analyze geographic diversification and growth patterns
- Track operational leverage and scalability metrics
- Output structured JSON format for all operational metrics
- Include industry-specific operational KPIs

Always provide specific operational metrics, unit economics, and segment performance data."""

    async def analyze(self, ticker: str, context: Optional[Dict[str, Any]] = None) -> AgentResult:
        """Perform operational metrics and segment analysis"""
        import time
        start_time = time.time()

        try:
            self.logger.logger.info(f"Operational Metrics Analyst analyzing {ticker}")

            # Get financial and operational data
            stock_data = await self.data_provider.get_stock_data(ticker)
            financial_metrics = await self.data_provider.get_financial_metrics(ticker)

            # Get SEC filings for segment data
            sec_filings = await self.sec_provider.get_multiple_filings(ticker, ["10-K", "10-Q"], 4)

            if not stock_data:
                raise ValueError(f"Unable to get stock data for {ticker}")

            # Calculate operational metrics
            operational_metrics = await self._calculate_operational_metrics(ticker, financial_metrics, sec_filings)

            # Prepare analysis prompt
            analysis_prompt = f"""Perform comprehensive operational metrics and segment analysis for {ticker}:

COMPANY OVERVIEW:
- Ticker: {ticker}
- Market Cap: ${stock_data.market_cap/1e9:.1f}B (if available)
- Current Price: ${stock_data.current_price:.2f}

OPERATIONAL METRICS:
{json.dumps(operational_metrics.__dict__ if operational_metrics else {}, indent=2)}

SEC FILINGS DATA:
- Filings analyzed: {len(sec_filings)}
- Segment data available: {any(f.segment_data for f in sec_filings)}

Provide comprehensive operational analysis including:

1. UNIT SALES ANALYSIS:
   - Key product/service unit sales trends
   - Unit sales growth by product category
   - Market share analysis where available

2. SEGMENT OPERATING PERFORMANCE:
   - Operating margins by business segment
   - Segment revenue growth rates
   - Cross-segment performance comparison

3. GEOGRAPHIC ANALYSIS:
   - International vs domestic revenue breakdown
   - Geographic growth rates and trends
   - Currency impact analysis

4. CUSTOMER METRICS:
   - Average revenue per user (ARPU) trends
   - Customer acquisition costs and trends
   - Customer lifetime value analysis
   - Churn and retention rates (if available)

5. OPERATIONAL EFFICIENCY:
   - Operating leverage analysis
   - Scalability metrics
   - Capacity utilization rates

6. INDUSTRY-SPECIFIC METRICS:
   - Backlog analysis (for applicable industries)
   - Order book trends
   - Delivery metrics and fulfillment rates

Output all metrics in structured JSON format with clear data source attribution."""

            # Get LLM analysis
            messages = [{"role": "user", "content": [{"text": analysis_prompt}]}]

            response, cost_info = await self.bedrock_client.converse_async(
                messages=messages,
                system_message=self.get_system_prompt(),
                max_tokens=2500,
                temperature=0.1
            )

            # Extract response text
            response_text = ""
            output_message = response.get('output', {}).get('message', {})
            content = output_message.get('content', [])

            for content_block in content:
                if 'text' in content_block:
                    response_text += content_block['text']

            # Extract key metrics
            key_metrics = {
                "segments_analyzed": len(operational_metrics.operating_margins_by_segment) if operational_metrics else 0,
                "geographic_regions": len(operational_metrics.geographic_revenue_split) if operational_metrics else 0,
                "has_unit_sales_data": bool(operational_metrics.unit_sales_data) if operational_metrics else False,
                "has_customer_metrics": bool(operational_metrics.customer_metrics) if operational_metrics else False,
                "data_sources": ["yfinance", "SEC EDGAR"],
                "analysis_timestamp": datetime.now().isoformat()
            }

            # Generate recommendations
            recommendations = self._extract_operational_recommendations(operational_metrics)

            execution_time = (time.time() - start_time) * 1000

            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=response_text,
                confidence=0.85,
                key_metrics=key_metrics,
                recommendations=recommendations,
                timestamp=datetime.now().isoformat(),
                execution_time_ms=execution_time,
                cost=cost_info['total_cost']
            )

        except Exception as e:
            self.logger.logger.error(f"Operational metrics analysis failed for {ticker}: {str(e)}")
            execution_time = (time.time() - start_time) * 1000

            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=f"Operational metrics analysis failed: {str(e)}",
                confidence=0.0,
                key_metrics={},
                recommendations=[],
                timestamp=datetime.now().isoformat(),
                execution_time_ms=execution_time,
                cost=0.0
            )

    async def _calculate_operational_metrics(
        self,
        ticker: str,
        financial_metrics,
        sec_filings
    ) -> Optional[OperationalMetrics]:
        """Calculate operational metrics from available data"""
        try:
            # Initialize metrics structure
            unit_sales_data = {}
            operating_margins_by_segment = {}
            geographic_revenue_split = {}
            customer_metrics = {}

            # Extract segment data from SEC filings
            if sec_filings:
                for filing in sec_filings:
                    if filing.segment_data and filing.segment_data.get('segments'):
                        for segment in filing.segment_data['segments']:
                            segment_name = segment.get('name', 'Unknown')
                            segment_revenue = segment.get('revenue')

                            if segment_revenue:
                                # Store segment revenue data
                                if segment_name not in operating_margins_by_segment:
                                    operating_margins_by_segment[segment_name] = []

                                # Calculate operating margin if operating income available
                                # This is simplified - real implementation would extract operating income
                                operating_margins_by_segment[segment_name] = segment_revenue

            # Get additional data from yfinance
            stock = yf.Ticker(ticker)
            info = stock.info

            # Extract customer metrics if available
            if 'totalRevenue' in info and 'sharesOutstanding' in info:
                total_revenue = info['totalRevenue']
                shares_outstanding = info['sharesOutstanding']

                # Calculate revenue per share as a proxy metric
                customer_metrics['revenue_per_share'] = total_revenue / shares_outstanding if shares_outstanding else None

            return OperationalMetrics(
                ticker=ticker,
                unit_sales_data=unit_sales_data,
                operating_margins_by_segment=operating_margins_by_segment,
                geographic_revenue_split=geographic_revenue_split,
                backlog_analysis=None,  # Would need specific industry data
                arpu_metrics=None,  # Would need customer count data
                customer_metrics=customer_metrics,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            self.logger.logger.error(f"Error calculating operational metrics: {str(e)}")
            return None

    def _extract_operational_recommendations(self, metrics: Optional[OperationalMetrics]) -> List[str]:
        """Extract operational recommendations"""
        recommendations = []

        if not metrics:
            recommendations.append("Limited operational data available for comprehensive analysis")
            return recommendations

        # Segment analysis recommendations
        if metrics.operating_margins_by_segment:
            segment_count = len(metrics.operating_margins_by_segment)
            if segment_count > 1:
                recommendations.append(f"Diversified business with {segment_count} segments - monitor segment performance")
            else:
                recommendations.append("Single segment business - focus on operational efficiency")

        # Geographic diversification
        if metrics.geographic_revenue_split:
            if len(metrics.geographic_revenue_split) > 2:
                recommendations.append("Geographic diversification provides revenue stability")
            else:
                recommendations.append("Limited geographic diversification - consider expansion opportunities")

        # Customer metrics
        if metrics.customer_metrics:
            recommendations.append("Monitor customer acquisition costs and lifetime value trends")

        if not recommendations:
            recommendations.append("Operational metrics analysis requires more detailed segment reporting")

        return recommendations
