#!/usr/bin/env python3
"""
Test script to verify intent analysis fix
"""

import json

def test_json_extraction():
    """Test the JSON extraction logic"""
    
    # Simulate the problematic response
    response_text = """{
    "primary_intent": "compare",
    "secondary_intents": ["analyze"],
    "tickers": ["AMZN", "MSFT", "META"],
    "confidence": 0.95,
    "analysis_types": ["fundamental", "comprehensive"],
    "timeframe": "medium",
    "comparison_mode": true,
    "specific_metrics": ["revenue", "growth", "market_performance"]
}

Note: While AWS is mentioned, it's a division of Amazon (AMZN), so I included AMZN as the relevant ticker. The query is clearly asking for a comparative analysis of these tech companies' performance over the past year, focusing on their business performance and market results."""
    
    print("🧪 Testing JSON extraction fix")
    print("=" * 50)
    
    try:
        # Find the JSON object in the response
        json_start = response_text.find('{')
        json_end = response_text.rfind('}') + 1
        
        if json_start >= 0 and json_end > json_start:
            json_text = response_text[json_start:json_end]
            print(f"✅ JSON extracted: {json_text[:100]}...")
            
            intent_data = json.loads(json_text)
            print(f"✅ JSON parsed successfully")
            print(f"   Primary intent: {intent_data.get('primary_intent')}")
            print(f"   Tickers: {intent_data.get('tickers')}")
            print(f"   Comparison mode: {intent_data.get('comparison_mode')}")
            
            return True
        else:
            print("❌ No JSON found in response")
            return False
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_fallback_intent():
    """Test fallback intent analysis"""
    print("\n🔄 Testing fallback intent analysis")
    print("=" * 50)
    
    query = "how did aws performed vs microsoft and meta last year"
    query_lower = query.lower()
    
    # Basic intent detection
    if any(word in query_lower for word in ['compare', 'vs', 'versus', 'against']):
        primary_intent = 'compare'
        comparison_mode = True
        print("✅ Detected comparison intent")
    else:
        primary_intent = 'analyze'
        comparison_mode = False
    
    # Basic ticker extraction
    import re
    potential_tickers = re.findall(r'\b[A-Z]{1,5}\b', query.upper())
    
    # Map company names to tickers
    company_mappings = {
        'AWS': 'AMZN',
        'AMAZON': 'AMZN', 
        'MICROSOFT': 'MSFT',
        'META': 'META',
        'FACEBOOK': 'META',
        'GOOGLE': 'GOOGL',
        'APPLE': 'AAPL',
        'TESLA': 'TSLA'
    }
    
    tickers = []
    for word in query.upper().split():
        if word in company_mappings:
            tickers.append(company_mappings[word])
    
    print(f"✅ Fallback analysis complete:")
    print(f"   Intent: {primary_intent}")
    print(f"   Tickers: {tickers}")
    print(f"   Comparison: {comparison_mode}")
    
    return len(tickers) > 0

if __name__ == "__main__":
    print("🔧 Intent Analysis Fix Verification")
    print("=" * 60)
    
    json_test = test_json_extraction()
    fallback_test = test_fallback_intent()
    
    print(f"\n📊 Test Results:")
    print(f"   JSON Extraction: {'✅ PASS' if json_test else '❌ FAIL'}")
    print(f"   Fallback Analysis: {'✅ PASS' if fallback_test else '❌ FAIL'}")
    
    if json_test and fallback_test:
        print("\n🎉 All tests passed! Intent analysis should work correctly now.")
    else:
        print("\n⚠️ Some tests failed. Check the implementation.")
