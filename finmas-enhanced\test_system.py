#!/usr/bin/env python3
"""
Comprehensive Test Suite for FinMAS Enhanced

This script validates the entire command-line financial analysis system
without requiring AWS credentials for basic functionality tests.
"""

import sys
import asyncio
import os
from pathlib import Path
from typing import Dict, Any, List

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))


class SystemTester:
    """Comprehensive system tester"""
    
    def __init__(self):
        self.test_results: List[Dict[str, Any]] = []
        self.total_tests = 0
        self.passed_tests = 0
    
    def log_test(self, test_name: str, passed: bool, message: str = ""):
        """Log test result"""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
            print(f"✅ {test_name}")
        else:
            print(f"❌ {test_name}: {message}")
        
        self.test_results.append({
            "name": test_name,
            "passed": passed,
            "message": message
        })
    
    def test_imports(self):
        """Test all module imports"""
        print("\n🔍 Testing Module Imports")
        print("-" * 50)
        
        # Test core imports
        try:
            from config import Config, MODEL_PRICING, AVAILABLE_MODELS
            self.log_test("Config module import", True)
        except Exception as e:
            self.log_test("Config module import", False, str(e))
        
        try:
            from utils.logging_utils import setup_logging, get_logger
            self.log_test("Logging utils import", True)
        except Exception as e:
            self.log_test("Logging utils import", False, str(e))
        
        try:
            import src.bedrock_client
            self.log_test("Bedrock client import", True)
        except Exception as e:
            self.log_test("Bedrock client import", False, str(e))

        try:
            import src.data_sources
            self.log_test("Data sources import", True)
        except Exception as e:
            self.log_test("Data sources import", False, str(e))

        try:
            import src.intent_analyzer
            self.log_test("Intent analyzer import", True)
        except Exception as e:
            self.log_test("Intent analyzer import", False, str(e))

        try:
            import src.agents
            self.log_test("Agents import", True)
        except Exception as e:
            self.log_test("Agents import", False, str(e))

        try:
            import src.financial_analyst
            self.log_test("Financial analyst import", True)
        except Exception as e:
            self.log_test("Financial analyst import", False, str(e))
    
    def test_configuration(self):
        """Test configuration system"""
        print("\n⚙️ Testing Configuration System")
        print("-" * 50)
        
        try:
            from config import Config
            
            # Test config initialization
            config = Config()
            self.log_test("Config initialization", True)
            
            # Test validation
            is_valid = config.validate()
            self.log_test("Config validation (without AWS creds)", not is_valid)  # Should fail without creds
            
            # Test AWS session kwargs
            kwargs = config.get_aws_session_kwargs()
            has_region = 'region_name' in kwargs
            self.log_test("AWS session kwargs generation", has_region)
            
        except Exception as e:
            self.log_test("Configuration system", False, str(e))
    
    def test_logging_system(self):
        """Test logging system"""
        print("\n📝 Testing Logging System")
        print("-" * 50)
        
        try:
            from utils.logging_utils import setup_logging, get_logger
            
            # Test logger setup
            logger = setup_logging()
            self.log_test("Logger setup", logger is not None)
            
            # Test logger retrieval
            logger2 = get_logger()
            self.log_test("Logger retrieval", logger2 is not None)
            
            # Test cost tracking
            cost_summary = logger.cost_tracker.get_session_summary()
            has_cost_fields = all(key in cost_summary for key in ['total_cost', 'total_calls', 'total_tokens'])
            self.log_test("Cost tracking functionality", has_cost_fields)
            
        except Exception as e:
            self.log_test("Logging system", False, str(e))
    
    async def test_data_sources(self):
        """Test data sources (mock mode)"""
        print("\n📊 Testing Data Sources")
        print("-" * 50)
        
        try:
            import src.data_sources
            from config import Config

            config = Config()

            # Test provider initialization
            provider = src.data_sources.FinancialDataProvider(config)
            self.log_test("Data provider initialization", True)

            # Test ticker validation (this should work without API keys)
            async with provider:
                is_valid = await provider.validate_ticker("AAPL")
                self.log_test("Ticker validation", isinstance(is_valid, bool))

                # Test stock data retrieval
                try:
                    stock_data = await provider.get_stock_data("AAPL")
                    self.log_test("Stock data retrieval", stock_data is not None)
                except Exception as e:
                    self.log_test("Stock data retrieval", False, f"Expected with limited data access: {str(e)}")

        except Exception as e:
            self.log_test("Data sources", False, str(e))
    
    def test_intent_analysis_structure(self):
        """Test intent analysis structure (without LLM calls)"""
        print("\n🧠 Testing Intent Analysis Structure")
        print("-" * 50)
        
        try:
            import src.intent_analyzer

            # Test QueryIntent creation
            intent = src.intent_analyzer.QueryIntent(
                primary_intent="analyze",
                secondary_intents=["technical"],
                tickers=["AAPL"],
                confidence=0.8,
                analysis_types=["comprehensive"]
            )
            self.log_test("QueryIntent creation", intent.primary_intent == "analyze")

            # Test fallback analysis
            # Note: We can't test full LLM analysis without AWS credentials
            self.log_test("Intent analyzer structure", True)

        except Exception as e:
            self.log_test("Intent analysis structure", False, str(e))
    
    def test_agent_structure(self):
        """Test agent structure (without LLM calls)"""
        print("\n🤖 Testing Agent Structure")
        print("-" * 50)
        
        try:
            import src.agents

            # Test AgentResult creation
            result = src.agents.AgentResult(
                agent_name="Test Agent",
                ticker="AAPL",
                analysis="Test analysis",
                confidence=0.8,
                key_metrics={"test": "value"},
                recommendations=["Test recommendation"],
                timestamp="2024-01-01T00:00:00",
                execution_time_ms=100.0,
                cost=0.01
            )
            self.log_test("AgentResult creation", result.agent_name == "Test Agent")

            # Test agent class structure (can't instantiate without clients)
            self.log_test("Agent classes structure", True)

            # Test enhanced agent coordinator
            self.log_test("AgentCoordinator class available", hasattr(src.agents, 'AgentCoordinator'))

        except Exception as e:
            self.log_test("Agent structure", False, str(e))
    
    def test_cli_structure(self):
        """Test CLI structure"""
        print("\n💻 Testing CLI Structure")
        print("-" * 50)
        
        try:
            # Test CLI import
            import cli
            self.log_test("CLI module import", True)
            
            # Test CLI class
            cli_instance = cli.FinancialAnalysisCLI()
            self.log_test("CLI class instantiation", cli_instance is not None)
            
        except Exception as e:
            self.log_test("CLI structure", False, str(e))
    
    def test_docker_files(self):
        """Test Docker configuration files"""
        print("\n🐳 Testing Docker Configuration")
        print("-" * 50)
        
        # Test Dockerfile exists
        dockerfile_exists = Path("Dockerfile").exists()
        self.log_test("Dockerfile exists", dockerfile_exists)
        
        # Test docker-compose.yml exists
        compose_exists = Path("docker-compose.yml").exists()
        self.log_test("docker-compose.yml exists", compose_exists)
        
        # Test .env.example exists
        env_example_exists = Path(".env.example").exists()
        self.log_test(".env.example exists", env_example_exists)
        
        # Test .dockerignore exists
        dockerignore_exists = Path(".dockerignore").exists()
        self.log_test(".dockerignore exists", dockerignore_exists)
    
    def test_dependencies(self):
        """Test required dependencies"""
        print("\n📦 Testing Dependencies")
        print("-" * 50)
        
        required_packages = [
            "boto3",
            "yfinance",
            "pandas",
            "aiohttp",
            "asyncio"
        ]
        
        for package in required_packages:
            try:
                __import__(package)
                self.log_test(f"Package {package}", True)
            except ImportError:
                self.log_test(f"Package {package}", False, "Not installed")
    
    async def run_all_tests(self):
        """Run all tests"""
        print("🚀 FinMAS Enhanced - Comprehensive System Test")
        print("=" * 80)
        
        # Run synchronous tests
        self.test_imports()
        self.test_configuration()
        self.test_logging_system()
        self.test_intent_analysis_structure()
        self.test_agent_structure()
        self.test_cli_structure()
        self.test_docker_files()
        self.test_dependencies()
        
        # Run asynchronous tests
        await self.test_data_sources()
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 80)
        print("🎯 TEST SUMMARY")
        print("=" * 80)
        
        success_rate = (self.passed_tests / self.total_tests) * 100 if self.total_tests > 0 else 0
        
        print(f"Total Tests: {self.total_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.total_tests - self.passed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("\n🎉 Excellent! System is ready for deployment.")
        elif success_rate >= 75:
            print("\n✅ Good! System is mostly functional with minor issues.")
        elif success_rate >= 50:
            print("\n⚠️ Warning! System has significant issues that need attention.")
        else:
            print("\n❌ Critical! System has major issues and needs fixes.")
        
        print("\n📋 Next Steps:")
        if self.passed_tests == self.total_tests:
            print("1. Set up AWS credentials in .env file")
            print("2. Run: docker-compose up --build")
            print("3. Or run directly: python cli.py")
            print("4. Start analyzing stocks!")
        else:
            print("1. Fix the failing tests above")
            print("2. Re-run this test suite")
            print("3. Once all tests pass, proceed with deployment")
        
        return success_rate >= 75


async def main():
    """Main test runner"""
    tester = SystemTester()
    success = await tester.run_all_tests()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
