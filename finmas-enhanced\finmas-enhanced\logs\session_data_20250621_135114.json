{"session_id": "20250621_135114", "timestamp": "2025-06-21T10:51:14.992608+00:00", "cost_summary": {"total_cost": 0.0, "total_calls": 0, "total_tokens": 0, "model_breakdown": {}, "average_cost_per_call": 0}, "function_calls": [], "agent_executions": [], "bedrock_api_calls": [], "api_call_logs": [{"timestamp": "2025-06-21T10:51:14.164763+00:00", "api_name": "test_api", "endpoint": "/test/endpoint", "method": "GET", "parameters": {"param1": "value1", "param2": 123}, "response_summary": "Test response with 5 fields", "execution_time_ms": 150.5, "success": true, "status_code": 200, "error_message": null}, {"timestamp": "2025-06-21T10:51:14.979563+00:00", "api_name": "yfinance", "endpoint": "ticker/AAPL/info", "method": "GET", "parameters": {"ticker": "AAPL"}, "response_summary": "Stock info with 180 fields", "execution_time_ms": 813.3158683776855, "success": true, "status_code": null, "error_message": null}], "ai_prompt_calls": [{"timestamp": "2025-06-21T10:51:14.165313+00:00", "model": "test-model", "system_prompt": "You are a helpful assistant.", "user_prompt": "What is the capital of France?", "response": "The capital of France is Paris.", "input_tokens": 15, "output_tokens": 8, "total_tokens": 23, "cost": 0.0023, "execution_time_ms": 1200.0, "agent_name": "test_agent", "function_calls": null, "success": true, "error_message": null}], "summary_stats": {"total_ai_calls": 1, "total_api_calls": 2, "total_function_calls": 0, "total_cost": 0.0, "total_tokens": 23}}