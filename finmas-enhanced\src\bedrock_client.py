"""
Enhanced AWS Bedrock Client for Financial Analysis

This module provides an async Bedrock client with comprehensive
cost tracking and function calling capabilities.
"""

import boto3
import asyncio
import time
from typing import Dict, List, Any, Optional, Tuple
from botocore.exceptions import ClientError, BotoCoreError

from config import Config
from utils.logging_utils import get_logger


class BedrockClient:
    """Enhanced async Bedrock client with cost tracking"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger()
        
        # Initialize boto3 session
        session_kwargs = config.get_aws_session_kwargs()
        session = boto3.Session(**session_kwargs)
        self.client = session.client('bedrock-runtime')
        
        self.logger.logger.info(f"Bedrock client initialized with model: {config.bedrock_model}")
    
    async def converse_async(
        self,
        messages: List[Dict[str, Any]],
        system_message: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        tools: Optional[List[Dict[str, Any]]] = None
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Async wrapper for Bedrock Converse API
        
        Args:
            messages: List of conversation messages
            system_message: Optional system message
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            tools: Optional function calling tools
            
        Returns:
            Tuple of (response, cost_info)
        """
        # Use config defaults if not specified
        max_tokens = max_tokens or self.config.max_tokens
        temperature = temperature or self.config.temperature
        
        # Prepare request
        request_body = {
            "modelId": self.config.bedrock_model,
            "messages": messages,
            "inferenceConfig": {
                "maxTokens": max_tokens,
                "temperature": temperature
            }
        }
        
        if system_message:
            request_body["system"] = [{"text": system_message}]
        
        if tools:
            request_body["toolConfig"] = {"tools": tools}
        
        # Execute with retry logic
        start_time = time.time()
        
        for attempt in range(self.config.max_retries):
            try:
                # Run in thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                response = await loop.run_in_executor(
                    None, 
                    lambda: self.client.converse(**request_body)
                )
                
                duration_ms = (time.time() - start_time) * 1000
                
                # Extract usage information
                usage = response.get('usage', {})
                input_tokens = usage.get('inputTokens', 0)
                output_tokens = usage.get('outputTokens', 0)
                
                # Calculate cost
                cost_info = self._calculate_cost(input_tokens, output_tokens, duration_ms)
                
                # Log API call to cost tracker
                self.logger.cost_tracker.log_api_call(
                    model_id=self.config.bedrock_model,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                    function_name="converse_async",
                    duration_ms=duration_ms,
                    success=True
                )

                # Extract prompts and response for detailed logging
                system_prompt = system_message if system_message else ""

                user_prompt = ""
                for message in messages:
                    if message.get('role') == 'user':
                        content = message.get('content', [])
                        for content_block in content:
                            if 'text' in content_block:
                                user_prompt += content_block['text'] + "\n"

                ai_response = ""
                output_message = response.get('output', {}).get('message', {})
                content = output_message.get('content', [])
                for content_block in content:
                    if 'text' in content_block:
                        ai_response += content_block['text']

                # Extract function calls if any
                function_calls = None
                for content_block in content:
                    if 'toolUse' in content_block:
                        if function_calls is None:
                            function_calls = []
                        tool_use = content_block['toolUse']
                        function_calls.append({
                            'name': tool_use.get('name'),
                            'input': tool_use.get('input')
                        })

                # Log detailed AI prompt call
                self.logger.log_ai_prompt(
                    model=self.config.bedrock_model,
                    system_prompt=system_prompt.strip(),
                    user_prompt=user_prompt.strip(),
                    response=ai_response,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                    cost=cost_info['total_cost'],
                    execution_time_ms=duration_ms,
                    function_calls=function_calls,
                    success=True
                )

                self.logger.logger.info(
                    f"Bedrock API call successful - Tokens: {input_tokens}+{output_tokens}="
                    f"{input_tokens + output_tokens}, Cost: ${cost_info['total_cost']:.4f}"
                )
                
                return response, cost_info
                
            except (ClientError, BotoCoreError) as e:
                error_message = str(e)
                self.logger.logger.warning(
                    f"Bedrock API call attempt {attempt + 1} failed: {error_message}"
                )
                
                if attempt < self.config.max_retries - 1:
                    await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
                else:
                    # Log failed API call
                    duration_ms = (time.time() - start_time) * 1000
                    self.logger.cost_tracker.log_api_call(
                        model_id=self.config.bedrock_model,
                        input_tokens=0,
                        output_tokens=0,
                        function_name="converse_async",
                        duration_ms=duration_ms,
                        success=False,
                        error_message=error_message
                    )
                    raise
    
    async def converse_with_functions(
        self,
        messages: List[Dict[str, Any]],
        tools: List[Dict[str, Any]],
        system_message: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None
    ) -> Tuple[str, List[Dict[str, Any]], Dict[str, Any]]:
        """
        Converse with function calling support
        
        Args:
            messages: Conversation messages
            tools: Available function tools
            system_message: Optional system message
            max_tokens: Maximum tokens
            temperature: Sampling temperature
            
        Returns:
            Tuple of (response_text, function_calls, cost_info)
        """
        response, cost_info = await self.converse_async(
            messages=messages,
            system_message=system_message,
            max_tokens=max_tokens,
            temperature=temperature,
            tools=tools
        )
        
        # Extract response content
        output_message = response.get('output', {}).get('message', {})
        content = output_message.get('content', [])
        
        response_text = ""
        function_calls = []
        
        for content_block in content:
            if 'text' in content_block:
                response_text += content_block['text']
            elif 'toolUse' in content_block:
                tool_use = content_block['toolUse']
                function_calls.append({
                    'toolUseId': tool_use['toolUseId'],
                    'name': tool_use['name'],
                    'input': tool_use['input']
                })
        
        return response_text, function_calls, cost_info
    
    def _calculate_cost(self, input_tokens: int, output_tokens: int, duration_ms: float) -> Dict[str, Any]:
        """Calculate cost for API call"""
        from config import MODEL_PRICING
        
        pricing = MODEL_PRICING.get(
            self.config.bedrock_model, 
            MODEL_PRICING["us.anthropic.claude-3-5-sonnet-20241022-v2:0"]
        )
        
        input_cost = (input_tokens / 1000) * pricing["input"]
        output_cost = (output_tokens / 1000) * pricing["output"]
        total_cost = input_cost + output_cost
        
        return {
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "total_tokens": input_tokens + output_tokens,
            "input_cost": input_cost,
            "output_cost": output_cost,
            "total_cost": total_cost,
            "duration_ms": duration_ms
        }
    
    def get_cost_summary(self) -> Dict[str, Any]:
        """Get session cost summary"""
        return self.logger.cost_tracker.get_session_summary()
    
    async def health_check(self) -> bool:
        """Check if Bedrock client is working"""
        try:
            test_messages = [
                {
                    "role": "user",
                    "content": [{"text": "Hello, respond with just 'OK'"}]
                }
            ]
            
            response, _ = await self.converse_async(
                messages=test_messages,
                max_tokens=10,
                temperature=0.1
            )
            
            return True
            
        except Exception as e:
            self.logger.logger.error(f"Bedrock health check failed: {str(e)}")
            return False
