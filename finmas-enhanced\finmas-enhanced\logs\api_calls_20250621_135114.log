2025-06-21 13:51:14,164 - API_CALL - {
  "timestamp": "2025-06-21T10:51:14.164763+00:00",
  "api_name": "test_api",
  "endpoint": "/test/endpoint",
  "method": "GET",
  "parameters": {
    "param1": "value1",
    "param2": 123
  },
  "response_summary": "Test response with 5 fields",
  "execution_time_ms": 150.5,
  "success": true,
  "status_code": 200,
  "error_message": null
}
2025-06-21 13:51:14,979 - API_CALL - {
  "timestamp": "2025-06-21T10:51:14.979563+00:00",
  "api_name": "yfinance",
  "endpoint": "ticker/AAPL/info",
  "method": "GET",
  "parameters": {
    "ticker": "AAPL"
  },
  "response_summary": "Stock info with 180 fields",
  "execution_time_ms": 813.3158683776855,
  "success": true,
  "status_code": null,
  "error_message": null
}
