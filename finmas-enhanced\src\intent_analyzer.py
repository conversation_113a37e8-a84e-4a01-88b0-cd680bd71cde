"""
LLM-Driven Intent Analysis for Financial Queries

This module uses AWS Bedrock to analyze user queries and determine
intent without hardcoded patterns or regex matching.
"""

import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from .bedrock_client import BedrockClient
from utils.logging_utils import get_logger


@dataclass
class QueryIntent:
    """Represents the analyzed intent of a user query"""
    primary_intent: str
    secondary_intents: List[str]
    tickers: List[str]
    confidence: float
    analysis_types: List[str]
    timeframe: Optional[str] = None
    comparison_mode: bool = False
    specific_metrics: List[str] = None
    
    def __post_init__(self):
        if self.specific_metrics is None:
            self.specific_metrics = []


class IntentAnalyzer:
    """LLM-driven intent analysis for financial queries"""
    
    def __init__(self, bedrock_client: BedrockClient):
        self.bedrock_client = bedrock_client
        self.logger = get_logger()
        
        # Function calling tools for intent analysis
        self.intent_analysis_tools = [
            {
                "toolSpec": {
                    "name": "analyze_query_intent",
                    "description": "Analyze user query to determine financial analysis intent and extract relevant information",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "primary_intent": {
                                    "type": "string",
                                    "enum": ["analyze", "technical", "fundamental", "news", "recommendation", "compare", "portfolio", "market", "education"],
                                    "description": "The main goal of the user's query"
                                },
                                "secondary_intents": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "Additional aspects the user might be interested in"
                                },
                                "tickers": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "Stock ticker symbols mentioned or implied in the query"
                                },
                                "confidence": {
                                    "type": "number",
                                    "minimum": 0.0,
                                    "maximum": 1.0,
                                    "description": "Confidence level in the analysis (0.0 to 1.0)"
                                },
                                "analysis_types": {
                                    "type": "array",
                                    "items": {
                                        "type": "string",
                                        "enum": ["comprehensive", "technical", "fundamental", "news", "recommendation"]
                                    },
                                    "description": "Types of analysis requested"
                                },
                                "timeframe": {
                                    "type": "string",
                                    "enum": ["short", "medium", "long"],
                                    "description": "Investment or analysis timeframe (short: 1-3 months, medium: 3-12 months, long: 1+ years)"
                                },
                                "comparison_mode": {
                                    "type": "boolean",
                                    "description": "Whether the user wants to compare multiple stocks"
                                },
                                "specific_metrics": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "Specific financial metrics the user is interested in"
                                }
                            },
                            "required": ["primary_intent", "tickers", "confidence", "analysis_types", "comparison_mode"]
                        }
                    }
                }
            }
        ]

        # System prompt for function calling
        self.system_prompt = """You are an expert financial analysis intent classifier. Analyze user queries about stocks and financial markets using the analyze_query_intent function.

Key guidelines:
- Extract stock ticker symbols carefully (AAPL, MSFT, GOOGL, etc.)
- Map company names to tickers: AWS=AMZN, Google=GOOGL, Facebook=META, Nike=NKE, Puma=PUMA
- Identify the primary intent and analysis types needed
- Determine if it's a comparison query (vs, versus, against, compared to)
- Set appropriate confidence based on clarity of the query
- Include relevant timeframes and specific metrics if mentioned

Always use the analyze_query_intent function to provide structured analysis."""
    
    async def analyze_query(self, query: str) -> QueryIntent:
        """
        Analyze a user query to determine intent and extract information
        
        Args:
            query: The user's natural language query
            
        Returns:
            QueryIntent object with analyzed information
        """
        try:
            self.logger.logger.info(f"Analyzing query intent: {query[:100]}...")
            
            # Prepare messages for Bedrock function calling
            messages = [
                {
                    "role": "user",
                    "content": [{"text": f"Analyze this financial query for intent and extract relevant information: '{query}'"}]
                }
            ]

            # Call Bedrock with function calling
            _, function_calls, _ = await self.bedrock_client.converse_with_functions(
                messages=messages,
                tools=self.intent_analysis_tools,
                system_message=self.system_prompt,
                max_tokens=1000,
                temperature=0.1
            )

            # Process function calls
            if function_calls:
                for func_call in function_calls:
                    if func_call['name'] == 'analyze_query_intent':
                        intent_data = func_call['input']

                        # Create QueryIntent from function call result
                        query_intent = QueryIntent(
                            primary_intent=intent_data.get('primary_intent', 'analyze'),
                            secondary_intents=intent_data.get('secondary_intents', []),
                            tickers=intent_data.get('tickers', []),
                            confidence=float(intent_data.get('confidence', 0.8)),
                            analysis_types=intent_data.get('analysis_types', ['comprehensive']),
                            timeframe=intent_data.get('timeframe'),
                            comparison_mode=bool(intent_data.get('comparison_mode', False)),
                            specific_metrics=intent_data.get('specific_metrics', [])
                        )

                        self.logger.logger.info(
                            f"Intent analysis complete: {query_intent.primary_intent}, "
                            f"tickers: {query_intent.tickers}, confidence: {query_intent.confidence}"
                        )

                        return query_intent

            # If no function calls or function call failed, use fallback
            self.logger.logger.warning("No function calls received, using fallback analysis")
            return self._fallback_intent_analysis(query)
        
        except Exception as e:
            self.logger.logger.error(f"Intent analysis failed: {str(e)}")
            return self._fallback_intent_analysis(query)
    
    def _fallback_intent_analysis(self, query: str) -> QueryIntent:
        """Fallback intent analysis using simple heuristics"""
        query_lower = query.lower()

        # Basic intent detection
        if any(word in query_lower for word in ['compare', 'vs', 'versus', 'against']):
            primary_intent = 'compare'
            comparison_mode = True
        elif any(word in query_lower for word in ['technical', 'chart', 'price', 'trend']):
            primary_intent = 'technical'
            comparison_mode = False
        elif any(word in query_lower for word in ['fundamental', 'earnings', 'revenue', 'valuation']):
            primary_intent = 'fundamental'
            comparison_mode = False
        elif any(word in query_lower for word in ['news', 'sentiment', 'recent']):
            primary_intent = 'news'
            comparison_mode = False
        elif any(word in query_lower for word in ['recommend', 'should i', 'invest', 'buy', 'sell']):
            primary_intent = 'recommendation'
            comparison_mode = False
        else:
            primary_intent = 'analyze'
            comparison_mode = False

        # Dynamic ticker extraction using company name mapping and pattern recognition
        import re

        # Basic company name to ticker mappings (minimal set for common cases)
        company_mappings = {
            'aws': 'AMZN',  # AWS is part of Amazon
            'amazon': 'AMZN',
            'microsoft': 'MSFT',
            'meta': 'META',
            'facebook': 'META',
            'google': 'GOOGL',
            'alphabet': 'GOOGL',
            'apple': 'AAPL',
            'tesla': 'TSLA',
            'nvidia': 'NVDA',
            'netflix': 'NFLX',
            'nike': 'NKE',
            'puma': 'PUMA',
            'adidas': 'ADS.DE'
        }

        tickers = []

        # First, look for company names
        for company, ticker in company_mappings.items():
            if company in query_lower:
                if ticker not in tickers:
                    tickers.append(ticker)

        # Then look for explicit ticker symbols (any 1-5 letter uppercase combinations)
        potential_tickers = re.findall(r'\b[A-Z]{1,5}\b', query.upper())

        # Add potential tickers (validation will happen later via live API)
        for ticker in potential_tickers:
            if ticker not in tickers and len(ticker) <= 5:
                tickers.append(ticker)

        return QueryIntent(
            primary_intent=primary_intent,
            secondary_intents=[],
            tickers=tickers,
            confidence=0.7,  # Slightly higher confidence with better mapping
            analysis_types=['comprehensive'],
            timeframe=None,
            comparison_mode=comparison_mode,
            specific_metrics=[]
        )
    
    async def validate_tickers(self, tickers: List[str]) -> List[str]:
        """
        Validate ticker symbols using live API data

        Args:
            tickers: List of potential ticker symbols

        Returns:
            List of validated ticker symbols
        """
        if not tickers:
            return []

        try:
            # Import here to avoid circular imports
            from .data_sources import FinancialDataProvider
            from config import Config

            config = Config()
            data_provider = FinancialDataProvider(config)

            validated_tickers = []

            async with data_provider:
                for ticker in tickers:
                    try:
                        # Try to validate using live API
                        is_valid = await data_provider.validate_ticker(ticker.upper())
                        if is_valid:
                            validated_tickers.append(ticker.upper())
                            self.logger.logger.info(f"Ticker {ticker} validated successfully")
                        else:
                            self.logger.logger.warning(f"Ticker {ticker} validation failed")
                    except Exception as e:
                        self.logger.logger.warning(f"Could not validate ticker {ticker}: {str(e)}")

            self.logger.logger.info(f"Validated tickers: {tickers} -> {validated_tickers}")
            return validated_tickers

        except Exception as e:
            self.logger.logger.error(f"Ticker validation failed: {str(e)}")
            # Fallback: return original tickers and let downstream handle validation
            return tickers
