"""
Comprehensive Logging System for FinMAS Enhanced

This module provides advanced logging capabilities including:
- Function call tracking with timestamps
- AWS Bedrock API cost tracking
- Token usage monitoring
- Performance metrics
- Real-time cost analytics
"""

import logging
import json
import time
import functools
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Callable
from pathlib import Path
import threading
from dataclasses import dataclass, asdict
from collections import defaultdict


import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent))

@dataclass
class FunctionCall:
    """Represents a logged function call"""
    function_name: str
    timestamp: str
    duration_ms: float
    parameters: Dict[str, Any]
    result_summary: str
    success: bool
    error_message: Optional[str] = None


@dataclass
class BedrockAPICall:
    """Represents a Bedrock API call with cost information"""
    timestamp: str
    model_id: str
    input_tokens: int
    output_tokens: int
    total_tokens: int
    input_cost: float
    output_cost: float
    total_cost: float
    function_name: str
    duration_ms: float
    success: bool
    error_message: Optional[str] = None


@dataclass
class AgentExecution:
    """Represents a CrewAI agent execution"""
    agent_name: str
    task_name: str
    start_time: str
    end_time: Optional[str]
    duration_ms: Optional[float]
    status: str  # 'running', 'completed', 'failed'
    output_length: int
    token_usage: Dict[str, int]
    cost: float
    error_message: Optional[str] = None


@dataclass
class AIPromptCall:
    """Represents an AI/LLM prompt call for detailed tracking"""
    timestamp: str
    model: str
    system_prompt: str
    user_prompt: str
    response: str
    input_tokens: int
    output_tokens: int
    total_tokens: int
    cost: float
    execution_time_ms: float
    agent_name: Optional[str] = None
    function_calls: Optional[List[Dict[str, Any]]] = None
    success: bool = True
    error_message: Optional[str] = None


@dataclass
class APICallLog:
    """Represents an API call with parameters for tracking"""
    timestamp: str
    api_name: str
    endpoint: str
    method: str
    parameters: Dict[str, Any]
    response_summary: str
    execution_time_ms: float
    success: bool
    status_code: Optional[int] = None
    error_message: Optional[str] = None


class CostTracker:
    """Tracks costs for AWS Bedrock API calls"""
    
    # AWS Bedrock pricing per 1000 tokens (as of 2024)
    PRICING = {
        "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {
            "input": 0.003,   # $3 per 1M input tokens
            "output": 0.015   # $15 per 1M output tokens
        },
        "anthropic.claude-3-sonnet-20240229-v1:0": {
            "input": 0.003,
            "output": 0.015
        },
        "anthropic.claude-3-haiku-20240307-v1:0": {
            "input": 0.00025,
            "output": 0.00125
        }
    }
    
    def __init__(self):
        self.total_cost = 0.0
        self.session_costs = defaultdict(float)
        self.model_costs = defaultdict(float)
        self.api_calls: List[BedrockAPICall] = []
        self._lock = threading.Lock()
    
    def calculate_cost(self, model_id: str, input_tokens: int, output_tokens: int) -> tuple[float, float, float]:
        """Calculate cost for a Bedrock API call"""
        pricing = self.PRICING.get(model_id, self.PRICING["us.anthropic.claude-3-5-sonnet-20241022-v2:0"])
        
        input_cost = (input_tokens / 1000) * pricing["input"]
        output_cost = (output_tokens / 1000) * pricing["output"]
        total_cost = input_cost + output_cost
        
        return input_cost, output_cost, total_cost
    
    def log_api_call(
        self,
        model_id: str,
        input_tokens: int,
        output_tokens: int,
        function_name: str,
        duration_ms: float,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> BedrockAPICall:
        """Log a Bedrock API call with cost calculation"""
        input_cost, output_cost, total_cost = self.calculate_cost(model_id, input_tokens, output_tokens)
        
        api_call = BedrockAPICall(
            timestamp=datetime.now(timezone.utc).isoformat(),
            model_id=model_id,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            total_tokens=input_tokens + output_tokens,
            input_cost=input_cost,
            output_cost=output_cost,
            total_cost=total_cost,
            function_name=function_name,
            duration_ms=duration_ms,
            success=success,
            error_message=error_message
        )
        
        with self._lock:
            self.api_calls.append(api_call)
            self.total_cost += total_cost
            self.model_costs[model_id] += total_cost
        
        return api_call
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get summary of current session costs"""
        with self._lock:
            return {
                "total_cost": round(self.total_cost, 4),
                "total_calls": len(self.api_calls),
                "total_tokens": sum(call.total_tokens for call in self.api_calls),
                "model_breakdown": dict(self.model_costs),
                "average_cost_per_call": round(self.total_cost / len(self.api_calls), 4) if self.api_calls else 0
            }


class PerformanceMonitor:
    """Monitors performance metrics for functions and agents"""
    
    def __init__(self):
        self.function_calls: List[FunctionCall] = []
        self.agent_executions: List[AgentExecution] = []
        self._lock = threading.Lock()
    
    def log_function_call(
        self,
        function_name: str,
        duration_ms: float,
        parameters: Dict[str, Any],
        result_summary: str,
        success: bool,
        error_message: Optional[str] = None
    ) -> FunctionCall:
        """Log a function call"""
        call = FunctionCall(
            function_name=function_name,
            timestamp=datetime.now(timezone.utc).isoformat(),
            duration_ms=duration_ms,
            parameters=parameters,
            result_summary=result_summary,
            success=success,
            error_message=error_message
        )
        
        with self._lock:
            self.function_calls.append(call)
        
        return call
    
    def start_agent_execution(self, agent_name: str, task_name: str) -> str:
        """Start tracking an agent execution"""
        execution = AgentExecution(
            agent_name=agent_name,
            task_name=task_name,
            start_time=datetime.now(timezone.utc).isoformat(),
            end_time=None,
            duration_ms=None,
            status="running",
            output_length=0,
            token_usage={},
            cost=0.0
        )
        
        with self._lock:
            self.agent_executions.append(execution)
        
        return execution.start_time
    
    def complete_agent_execution(
        self,
        start_time: str,
        output_length: int,
        token_usage: Dict[str, int],
        cost: float,
        success: bool = True,
        error_message: Optional[str] = None
    ):
        """Complete an agent execution"""
        end_time = datetime.now(timezone.utc).isoformat()
        
        with self._lock:
            for execution in self.agent_executions:
                if execution.start_time == start_time:
                    execution.end_time = end_time
                    execution.duration_ms = (
                        datetime.fromisoformat(end_time.replace('Z', '+00:00')) -
                        datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                    ).total_seconds() * 1000
                    execution.status = "completed" if success else "failed"
                    execution.output_length = output_length
                    execution.token_usage = token_usage
                    execution.cost = cost
                    execution.error_message = error_message
                    break


class FinMASLogger:
    """Main logging class for FinMAS Enhanced"""

    def __init__(self, log_dir: str = "finmas-enhanced/logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)

        self.cost_tracker = CostTracker()
        self.performance_monitor = PerformanceMonitor()

        # Setup file logging
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = self.log_dir / f"finmas_session_{self.session_id}.log"

        # Dedicated log files for API calls and AI prompts
        self.api_calls_log_file = self.log_dir / f"api_calls_{self.session_id}.log"
        self.ai_prompts_log_file = self.log_dir / f"ai_prompts_{self.session_id}.log"

        # Storage for detailed logging
        self.ai_prompt_calls: List[AIPromptCall] = []
        self.api_call_logs: List[APICallLog] = []

        # Setup main logger
        self.logger = logging.getLogger("finmas_enhanced")
        self.logger.setLevel(logging.INFO)

        # Clear existing handlers to prevent duplicates
        if self.logger.handlers:
            self.logger.handlers.clear()

        # Setup dedicated loggers
        self._setup_main_logger()
        self._setup_api_logger()
        self._setup_ai_prompt_logger()

        self.logger.info(f"FinMAS Enhanced session started: {self.session_id}")

    def _setup_main_logger(self):
        """Setup main application logger"""
        # File handler
        file_handler = logging.FileHandler(self.log_file)
        file_handler.setLevel(logging.INFO)

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

        # Prevent propagation to avoid duplicate logs
        self.logger.propagate = False

    def _setup_api_logger(self):
        """Setup dedicated API calls logger"""
        self.api_logger = logging.getLogger("finmas_api_calls")
        self.api_logger.setLevel(logging.INFO)

        # Clear existing handlers
        if self.api_logger.handlers:
            self.api_logger.handlers.clear()

        # API calls file handler
        api_handler = logging.FileHandler(self.api_calls_log_file)
        api_handler.setLevel(logging.INFO)

        # Detailed formatter for API calls
        api_formatter = logging.Formatter(
            '%(asctime)s - API_CALL - %(message)s'
        )
        api_handler.setFormatter(api_formatter)

        self.api_logger.addHandler(api_handler)
        self.api_logger.propagate = False

    def _setup_ai_prompt_logger(self):
        """Setup dedicated AI prompts logger"""
        self.ai_logger = logging.getLogger("finmas_ai_prompts")
        self.ai_logger.setLevel(logging.INFO)

        # Clear existing handlers
        if self.ai_logger.handlers:
            self.ai_logger.handlers.clear()

        # AI prompts file handler
        ai_handler = logging.FileHandler(self.ai_prompts_log_file)
        ai_handler.setLevel(logging.INFO)

        # Detailed formatter for AI prompts
        ai_formatter = logging.Formatter(
            '%(asctime)s - AI_PROMPT - %(message)s'
        )
        ai_handler.setFormatter(ai_formatter)

        self.ai_logger.addHandler(ai_handler)
        self.ai_logger.propagate = False
    
    def log_function_call(self, func: Callable) -> Callable:
        """Decorator to log function calls"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            function_name = f"{func.__module__}.{func.__name__}"
            
            # Sanitize parameters for logging
            safe_params = self._sanitize_params(kwargs)
            
            try:
                result = func(*args, **kwargs)
                duration_ms = (time.time() - start_time) * 1000
                
                result_summary = self._create_result_summary(result)
                
                self.performance_monitor.log_function_call(
                    function_name=function_name,
                    duration_ms=duration_ms,
                    parameters=safe_params,
                    result_summary=result_summary,
                    success=True
                )
                
                self.logger.info(
                    f"Function {function_name} completed in {duration_ms:.2f}ms"
                )
                
                return result
                
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                
                self.performance_monitor.log_function_call(
                    function_name=function_name,
                    duration_ms=duration_ms,
                    parameters=safe_params,
                    result_summary="",
                    success=False,
                    error_message=str(e)
                )
                
                self.logger.error(
                    f"Function {function_name} failed after {duration_ms:.2f}ms: {str(e)}"
                )
                
                raise
        
        return wrapper

    def log_api_call(
        self,
        api_name: str,
        endpoint: str,
        method: str,
        parameters: Dict[str, Any],
        response_summary: str,
        execution_time_ms: float,
        success: bool = True,
        status_code: Optional[int] = None,
        error_message: Optional[str] = None
    ) -> APICallLog:
        """Log an API call with detailed parameters"""
        api_call = APICallLog(
            timestamp=datetime.now(timezone.utc).isoformat(),
            api_name=api_name,
            endpoint=endpoint,
            method=method,
            parameters=self._sanitize_params(parameters),
            response_summary=response_summary,
            execution_time_ms=execution_time_ms,
            success=success,
            status_code=status_code,
            error_message=error_message
        )

        # Store in memory
        self.api_call_logs.append(api_call)

        # Log to dedicated file
        log_message = json.dumps({
            "timestamp": api_call.timestamp,
            "api_name": api_call.api_name,
            "endpoint": api_call.endpoint,
            "method": api_call.method,
            "parameters": api_call.parameters,
            "response_summary": api_call.response_summary,
            "execution_time_ms": api_call.execution_time_ms,
            "success": api_call.success,
            "status_code": api_call.status_code,
            "error_message": api_call.error_message
        }, indent=2)

        self.api_logger.info(log_message)

        # Also log summary to main logger
        status = "SUCCESS" if success else "FAILED"
        self.logger.info(f"API Call [{api_name}] {method} {endpoint} - {status} ({execution_time_ms:.2f}ms)")

        return api_call

    def log_ai_prompt(
        self,
        model: str,
        system_prompt: str,
        user_prompt: str,
        response: str,
        input_tokens: int,
        output_tokens: int,
        cost: float,
        execution_time_ms: float,
        agent_name: Optional[str] = None,
        function_calls: Optional[List[Dict[str, Any]]] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> AIPromptCall:
        """Log an AI prompt call with full details"""
        ai_call = AIPromptCall(
            timestamp=datetime.now(timezone.utc).isoformat(),
            model=model,
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            response=response,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            total_tokens=input_tokens + output_tokens,
            cost=cost,
            execution_time_ms=execution_time_ms,
            agent_name=agent_name,
            function_calls=function_calls,
            success=success,
            error_message=error_message
        )

        # Store in memory
        self.ai_prompt_calls.append(ai_call)

        # Log to dedicated file with full details
        log_message = json.dumps({
            "timestamp": ai_call.timestamp,
            "model": ai_call.model,
            "agent_name": ai_call.agent_name,
            "system_prompt": ai_call.system_prompt,
            "user_prompt": ai_call.user_prompt,
            "response": ai_call.response,
            "input_tokens": ai_call.input_tokens,
            "output_tokens": ai_call.output_tokens,
            "total_tokens": ai_call.total_tokens,
            "cost": ai_call.cost,
            "execution_time_ms": ai_call.execution_time_ms,
            "function_calls": ai_call.function_calls,
            "success": ai_call.success,
            "error_message": ai_call.error_message
        }, indent=2)

        self.ai_logger.info(log_message)

        # Log summary to main logger
        status = "SUCCESS" if success else "FAILED"
        agent_info = f" [{agent_name}]" if agent_name else ""
        function_info = f" with {len(function_calls)} function calls" if function_calls else ""

        self.logger.info(
            f"AI Call{agent_info} {model} - {status} "
            f"({input_tokens}+{output_tokens}={ai_call.total_tokens} tokens, "
            f"${cost:.4f}, {execution_time_ms:.2f}ms){function_info}"
        )

        return ai_call
    
    def _sanitize_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize parameters for safe logging"""
        safe_params = {}
        for key, value in params.items():
            if isinstance(value, (str, int, float, bool, type(None))):
                safe_params[key] = value
            elif isinstance(value, (list, tuple)):
                safe_params[key] = f"<{type(value).__name__} of length {len(value)}>"
            else:
                safe_params[key] = f"<{type(value).__name__}>"
        return safe_params
    
    def _create_result_summary(self, result: Any) -> str:
        """Create a summary of function result"""
        if isinstance(result, str):
            return f"String of length {len(result)}"
        elif isinstance(result, (list, tuple)):
            return f"{type(result).__name__} of length {len(result)}"
        elif isinstance(result, dict):
            return f"Dict with keys: {list(result.keys())}"
        else:
            return f"{type(result).__name__}"
    
    def export_session_data(self) -> Dict[str, Any]:
        """Export all session data including API calls and AI prompts"""
        return {
            "session_id": self.session_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "cost_summary": self.cost_tracker.get_session_summary(),
            "function_calls": [asdict(call) for call in self.performance_monitor.function_calls],
            "agent_executions": [asdict(execution) for execution in self.performance_monitor.agent_executions],
            "bedrock_api_calls": [asdict(call) for call in self.cost_tracker.api_calls],
            "api_call_logs": [asdict(call) for call in self.api_call_logs],
            "ai_prompt_calls": [asdict(call) for call in self.ai_prompt_calls],
            "summary_stats": {
                "total_ai_calls": len(self.ai_prompt_calls),
                "total_api_calls": len(self.api_call_logs),
                "total_function_calls": len(self.performance_monitor.function_calls),
                "total_cost": self.cost_tracker.total_cost,
                "total_tokens": sum(call.total_tokens for call in self.ai_prompt_calls)
            }
        }
    
    def save_session_data(self):
        """Save session data to JSON file"""
        session_data = self.export_session_data()
        json_file = self.log_dir / f"session_data_{self.session_id}.json"
        
        with open(json_file, 'w') as f:
            json.dump(session_data, f, indent=2)
        
        self.logger.info(f"Session data saved to {json_file}")


def setup_logging(log_dir: str = "finmas-enhanced/logs") -> FinMASLogger:
    """Setup logging for FinMAS Enhanced"""
    return FinMASLogger(log_dir)


# Global logger instance with thread lock
_global_logger: Optional[FinMASLogger] = None
_logger_lock = threading.Lock()


def get_logger() -> FinMASLogger:
    """Get the global logger instance (thread-safe singleton)"""
    global _global_logger

    if _global_logger is None:
        with _logger_lock:
            # Double-check locking pattern
            if _global_logger is None:
                _global_logger = setup_logging()

    return _global_logger


def reset_logger():
    """Reset the global logger instance (useful for testing)"""
    global _global_logger
    with _logger_lock:
        if _global_logger is not None:
            # Clean up existing logger
            if _global_logger.logger.handlers:
                for handler in _global_logger.logger.handlers:
                    handler.close()
                _global_logger.logger.handlers.clear()
        _global_logger = None


def check_logger_status():
    """Check the current logger status for debugging"""
    logger = logging.getLogger("finmas_enhanced")
    return {
        "handler_count": len(logger.handlers),
        "handler_types": [type(h).__name__ for h in logger.handlers],
        "level": logger.level,
        "propagate": logger.propagate,
        "global_logger_exists": _global_logger is not None
    }
