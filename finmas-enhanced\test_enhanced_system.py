"""
Comprehensive Testing Suite for Enhanced Financial Analysis System

This module provides comprehensive tests for all new agents, data providers,
and function calling capabilities with real-world scenarios.
"""

import asyncio
import json
import pytest
from typing import Dict, List, Any, Optional
from datetime import datetime

# Import system components
from src.agents import AgentCoordinator
from src.bedrock_client import BedrockClient
from src.data_sources import FinancialDataProvider
from src.sec_data_provider import EnhancedSECDataProvider
from src.function_calling import FunctionCallOrchestrator
from src.specialized_agents import FinancialPerformanceAnalysisAgent, OperationalMetricsAgent
from src.risk_and_valuation_agents import RiskMonitoringAgent, ValuationAnalysisAgent
from src.strategic_analysis_agent import StrategicAnalysisAgent
from config import Config
from utils.logging_utils import get_logger


class EnhancedSystemTester:
    """Comprehensive tester for the enhanced financial analysis system"""
    
    def __init__(self):
        self.logger = get_logger()
        self.config = Config()
        self.test_results = {}
        
        # Test tickers for different scenarios
        self.test_tickers = {
            "large_cap": "AAPL",      # Large cap tech
            "financial": "JPM",       # Financial services
            "healthcare": "JNJ",      # Healthcare/pharma
            "energy": "XOM",          # Energy sector
            "small_cap": "ROKU"       # Smaller growth company
        }
    
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.logger.logger.info(f"{status} - {test_name}: {details}")
        self.test_results[test_name] = {"success": success, "details": details}
    
    async def test_sec_data_provider(self):
        """Test enhanced SEC data provider functionality"""
        self.logger.logger.info("Testing Enhanced SEC Data Provider...")
        
        try:
            sec_provider = EnhancedSECDataProvider(self.config)
            
            # Test latest filing retrieval
            ticker = self.test_tickers["large_cap"]
            filing_data = await sec_provider.get_latest_filing(ticker, "10-K")
            
            if filing_data:
                self.log_test("SEC Latest Filing Retrieval", True, f"Retrieved {filing_data.form_type} for {ticker}")
                
                # Test filing data structure
                required_fields = ["ticker", "form_type", "filing_date", "accession_number"]
                has_required_fields = all(hasattr(filing_data, field) for field in required_fields)
                self.log_test("SEC Filing Data Structure", has_required_fields, f"Required fields present: {has_required_fields}")
                
                # Test financial data extraction
                has_financial_data = any([
                    filing_data.revenue,
                    filing_data.net_income,
                    filing_data.total_assets
                ])
                self.log_test("SEC Financial Data Extraction", has_financial_data, f"Financial metrics extracted: {has_financial_data}")
            else:
                self.log_test("SEC Latest Filing Retrieval", False, f"No filing data retrieved for {ticker}")
            
            # Test multiple filings retrieval
            multiple_filings = await sec_provider.get_multiple_filings(ticker, ["10-K", "10-Q"], 4)
            self.log_test("SEC Multiple Filings Retrieval", len(multiple_filings) > 0, f"Retrieved {len(multiple_filings)} filings")
            
        except Exception as e:
            self.log_test("SEC Data Provider", False, f"Exception: {str(e)}")
    
    async def test_specialized_agents(self):
        """Test all specialized financial analysis agents"""
        self.logger.logger.info("Testing Specialized Agents...")
        
        try:
            # Initialize components
            bedrock_client = BedrockClient(self.config)
            data_provider = FinancialDataProvider(self.config)
            
            # Test Financial Performance Analysis Agent
            fp_agent = FinancialPerformanceAnalysisAgent(bedrock_client, data_provider, self.config)
            
            async with data_provider:
                ticker = self.test_tickers["large_cap"]
                fp_result = await fp_agent.analyze(ticker)
                
                self.log_test("Financial Performance Agent", 
                             fp_result.confidence > 0, 
                             f"Analysis completed with confidence: {fp_result.confidence}")
                
                # Test structured output
                has_structured_metrics = bool(fp_result.key_metrics)
                self.log_test("Financial Performance Structured Output", 
                             has_structured_metrics, 
                             f"Key metrics present: {len(fp_result.key_metrics) if fp_result.key_metrics else 0}")
            
            # Test Operational Metrics Agent
            om_agent = OperationalMetricsAgent(bedrock_client, data_provider, self.config)
            
            async with data_provider:
                om_result = await om_agent.analyze(ticker)
                self.log_test("Operational Metrics Agent", 
                             om_result.confidence > 0, 
                             f"Analysis completed with confidence: {om_result.confidence}")
            
            # Test Risk Monitoring Agent
            rm_agent = RiskMonitoringAgent(bedrock_client, data_provider, self.config)
            
            async with data_provider:
                rm_result = await rm_agent.analyze(ticker)
                self.log_test("Risk Monitoring Agent", 
                             rm_result.confidence > 0, 
                             f"Analysis completed with confidence: {rm_result.confidence}")
            
            # Test Valuation Analysis Agent
            va_agent = ValuationAnalysisAgent(bedrock_client, data_provider, self.config)
            
            async with data_provider:
                va_result = await va_agent.analyze(ticker)
                self.log_test("Valuation Analysis Agent", 
                             va_result.confidence > 0, 
                             f"Analysis completed with confidence: {va_result.confidence}")
            
            # Test Strategic Analysis Agent
            sa_agent = StrategicAnalysisAgent(bedrock_client, data_provider, self.config)
            
            async with data_provider:
                sa_result = await sa_agent.analyze(ticker)
                self.log_test("Strategic Analysis Agent", 
                             sa_result.confidence > 0, 
                             f"Analysis completed with confidence: {sa_result.confidence}")
                
        except Exception as e:
            self.log_test("Specialized Agents", False, f"Exception: {str(e)}")
    
    async def test_enhanced_function_calling(self):
        """Test enhanced function calling capabilities"""
        self.logger.logger.info("Testing Enhanced Function Calling...")
        
        try:
            bedrock_client = BedrockClient(self.config)
            data_provider = FinancialDataProvider(self.config)
            orchestrator = FunctionCallOrchestrator(bedrock_client, data_provider)
            
            # Test SEC filing function
            test_messages = [{
                "role": "user",
                "content": [{"text": f"Get the latest 10-K filing data for {self.test_tickers['large_cap']}"}]
            }]
            
            response_text, function_calls, cost_info = await orchestrator.execute_with_functions(
                messages=test_messages,
                system_message="You are a financial analyst. Use the available tools to answer user queries.",
                max_iterations=2
            )
            
            self.log_test("Enhanced Function Calling", 
                         len(function_calls) > 0, 
                         f"Function calls executed: {len(function_calls)}")
            
            # Test specialized agent functions
            specialized_test_messages = [{
                "role": "user",
                "content": [{"text": f"Analyze the financial performance of {self.test_tickers['large_cap']} including revenue growth and margin trends"}]
            }]
            
            spec_response, spec_calls, spec_cost = await orchestrator.execute_with_functions(
                messages=specialized_test_messages,
                system_message="You are a financial analyst. Use specialized analysis tools to provide comprehensive insights.",
                max_iterations=3
            )
            
            self.log_test("Specialized Function Calling", 
                         len(spec_calls) > 0, 
                         f"Specialized function calls: {len(spec_calls)}")
            
        except Exception as e:
            self.log_test("Enhanced Function Calling", False, f"Exception: {str(e)}")
    
    async def test_agent_coordinator_enhanced(self):
        """Test enhanced agent coordinator with all agent types"""
        self.logger.logger.info("Testing Enhanced Agent Coordinator...")
        
        try:
            bedrock_client = BedrockClient(self.config)
            data_provider = FinancialDataProvider(self.config)
            coordinator = AgentCoordinator(bedrock_client, data_provider)
            
            # Test comprehensive analysis with all agents
            ticker = self.test_tickers["large_cap"]
            
            # Test parallel execution
            parallel_results = await coordinator.run_collaborative_analysis(
                ticker=ticker,
                agent_types=["financial_performance", "operational_metrics", "risk_monitoring"],
                collaboration_mode="parallel"
            )
            
            self.log_test("Enhanced Coordinator Parallel", 
                         len(parallel_results) > 0, 
                         f"Parallel agents executed: {len(parallel_results)}")
            
            # Test hierarchical execution
            hierarchical_results = await coordinator.run_collaborative_analysis(
                ticker=ticker,
                agent_types=["technical", "fundamental", "financial_performance", "valuation_analysis", "investment"],
                collaboration_mode="hierarchical"
            )
            
            self.log_test("Enhanced Coordinator Hierarchical", 
                         len(hierarchical_results) > 0, 
                         f"Hierarchical agents executed: {len(hierarchical_results)}")
            
            # Test synthesis
            if hierarchical_results:
                synthesis = await coordinator.synthesize_results(
                    ticker=ticker,
                    agent_results=hierarchical_results,
                    synthesis_focus="comprehensive"
                )
                
                self.log_test("Enhanced Synthesis", 
                             len(synthesis) > 100, 
                             f"Synthesis length: {len(synthesis)} characters")
            
        except Exception as e:
            self.log_test("Enhanced Agent Coordinator", False, f"Exception: {str(e)}")
    
    async def test_multi_company_analysis(self):
        """Test multi-company comparative analysis"""
        self.logger.logger.info("Testing Multi-Company Analysis...")
        
        try:
            bedrock_client = BedrockClient(self.config)
            data_provider = FinancialDataProvider(self.config)
            orchestrator = FunctionCallOrchestrator(bedrock_client, data_provider)
            
            # Test company comparison
            comparison_tickers = [self.test_tickers["large_cap"], self.test_tickers["financial"]]
            
            test_messages = [{
                "role": "user",
                "content": [{"text": f"Compare the financial performance of {', '.join(comparison_tickers)} focusing on revenue growth, margins, and valuation metrics"}]
            }]
            
            response_text, function_calls, cost_info = await orchestrator.execute_with_functions(
                messages=test_messages,
                system_message="You are a financial analyst specializing in comparative analysis.",
                max_iterations=3
            )
            
            # Check if comparison function was called
            comparison_called = any("compare_companies" in str(call) for call in function_calls)
            
            self.log_test("Multi-Company Analysis", 
                         comparison_called, 
                         f"Comparison function called: {comparison_called}")
            
        except Exception as e:
            self.log_test("Multi-Company Analysis", False, f"Exception: {str(e)}")
    
    async def test_data_validation_and_attribution(self):
        """Test data validation and source attribution"""
        self.logger.logger.info("Testing Data Validation and Attribution...")
        
        try:
            data_provider = FinancialDataProvider(self.config)
            
            async with data_provider:
                # Test ticker validation
                valid_ticker = await data_provider.validate_ticker(self.test_tickers["large_cap"])
                invalid_ticker = await data_provider.validate_ticker("INVALID123")
                
                self.log_test("Ticker Validation", 
                             valid_ticker and not invalid_ticker, 
                             f"Valid: {valid_ticker}, Invalid: {invalid_ticker}")
                
                # Test data timestamps
                stock_data = await data_provider.get_stock_data(self.test_tickers["large_cap"])
                if stock_data:
                    has_timestamp = bool(stock_data.timestamp)
                    self.log_test("Data Timestamps", 
                                 has_timestamp, 
                                 f"Timestamp present: {stock_data.timestamp}")
                
                # Test error handling for missing data
                missing_data = await data_provider.get_stock_data("NONEXISTENT")
                self.log_test("Error Handling", 
                             missing_data is None, 
                             "Graceful handling of missing data")
            
        except Exception as e:
            self.log_test("Data Validation", False, f"Exception: {str(e)}")
    
    async def run_all_tests(self):
        """Run comprehensive test suite"""
        self.logger.logger.info("🚀 Starting Enhanced Financial Analysis System Tests")
        
        # Run all test categories
        await self.test_sec_data_provider()
        await self.test_specialized_agents()
        await self.test_enhanced_function_calling()
        await self.test_agent_coordinator_enhanced()
        await self.test_multi_company_analysis()
        await self.test_data_validation_and_attribution()
        
        # Generate test summary
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result["success"])
        failed_tests = total_tests - passed_tests
        
        self.logger.logger.info(f"📊 Test Summary: {passed_tests}/{total_tests} passed ({failed_tests} failed)")
        
        # Log failed tests
        if failed_tests > 0:
            self.logger.logger.warning("❌ Failed Tests:")
            for test_name, result in self.test_results.items():
                if not result["success"]:
                    self.logger.logger.warning(f"  - {test_name}: {result['details']}")
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0,
            "detailed_results": self.test_results
        }


async def main():
    """Main test execution function"""
    tester = EnhancedSystemTester()
    results = await tester.run_all_tests()
    
    print(f"\n🎯 Enhanced Financial Analysis System Test Results:")
    print(f"   Total Tests: {results['total_tests']}")
    print(f"   Passed: {results['passed_tests']}")
    print(f"   Failed: {results['failed_tests']}")
    print(f"   Success Rate: {results['success_rate']:.1%}")
    
    return results


if __name__ == "__main__":
    asyncio.run(main())
