# Project Structure

## Overview

This document outlines the clean, organized structure of the Enhanced Financial Analysis System (FinMAS) repository.

## Directory Structure

```
finmas-enhanced/
├── 📁 src/                          # Core source code
│   ├── 🐍 __init__.py              # Package initialization
│   ├── 🤖 agents.py                # Base agent classes and coordinator
│   ├── 🔧 specialized_agents.py    # Financial Performance & Operational Metrics agents
│   ├── ⚠️ risk_and_valuation_agents.py  # Risk Monitoring & Valuation Analysis agents
│   ├── 📈 strategic_analysis_agent.py   # Strategic Analysis agent
│   ├── 🔄 function_calling.py      # Enhanced function orchestration with temporal support
│   ├── 📊 data_sources.py          # Financial data providers with temporal capabilities
│   ├── 📋 sec_data_provider.py     # Complete SEC document retrieval
│   ├── 🧠 bedrock_client.py        # AWS Bedrock integration
│   ├── 🎯 intent_analyzer.py       # Query intent analysis
│   ├── 💼 financial_analyst.py     # Financial analysis utilities
│   └── 🔗 agent_communication.py   # Agent communication protocols
│
├── 📁 utils/                        # Utility modules
│   ├── 🐍 __init__.py              # Package initialization
│   ├── 📝 logging_utils.py         # Comprehensive logging system
│   └── 🛠️ bedrock_utils.py         # Bedrock helper functions
│
├── 📁 docs/                         # Documentation
│   ├── 📄 TEMPORAL_ENHANCEMENTS_SUMMARY.md  # Temporal features documentation
│   └── 📄 PROJECT_STRUCTURE.md     # This file
│
├── 📁 examples/                     # Usage examples and demonstrations
│   └── 🚀 enhanced_system_demo.py  # Comprehensive system demonstration
│
├── 📁 logs/                         # System logs (runtime generated)
│   ├── 🤖 ai_prompts_*.log         # AI interaction logs
│   ├── 📞 api_calls_*.log          # API call logs with cost tracking
│   └── 📊 finmas_session_*.log     # Session logs
│
├── 📄 README.md                     # Comprehensive system documentation
├── ⚙️ config.py                    # Configuration management
├── 💻 cli.py                       # Command-line interface
├── 📦 requirements.txt             # Python dependencies
├── 📋 pyproject.toml               # Project metadata
├── 🐳 Dockerfile                   # Docker container configuration
├── 🐳 docker-compose.yml           # Docker Compose configuration
└── 🐍 __init__.py                  # Root package initialization
```

## Core Components

### 🤖 Agent System (`src/`)

#### Base Infrastructure
- **`agents.py`**: Base agent classes, coordinator, and communication protocols
- **`agent_communication.py`**: Inter-agent communication and coordination

#### Specialized Agents (5 Categories)
1. **`specialized_agents.py`**:
   - Financial Performance Analysis Agent
   - Operational Metrics Agent

2. **`risk_and_valuation_agents.py`**:
   - Risk Monitoring Agent
   - Valuation Analysis Agent

3. **`strategic_analysis_agent.py`**:
   - Strategic Analysis Agent

#### Enhanced Function Calling
- **`function_calling.py`**: Enhanced orchestration with temporal parameters
  - `get_temporal_financial_data()` - Period-specific financial metrics
  - `get_complete_sec_document()` - Full SEC document retrieval
  - `get_sec_filing_data()` - Enhanced SEC filing analysis

### 📊 Data Integration (`src/`)

#### Financial Data Sources
- **`data_sources.py`**: Enhanced financial data providers
  - Temporal financial metrics retrieval
  - Quarter/year-specific data extraction
  - Multi-period comparative analysis

#### SEC Document Processing
- **`sec_data_provider.py`**: Complete SEC document retrieval
  - Full 10-K/10-Q content extraction
  - Period-specific filing retrieval
  - Complete document section analysis

#### AI Integration
- **`bedrock_client.py`**: AWS Bedrock integration
  - Function calling orchestration
  - Cost tracking and monitoring
  - Response processing

### 🛠️ Utilities (`utils/`)

- **`logging_utils.py`**: Comprehensive logging system
  - AI prompt/response logging
  - API call tracking with costs
  - Session management
  - Error tracking

- **`bedrock_utils.py`**: Bedrock helper functions
  - Authentication management
  - Request formatting
  - Response parsing

### 📖 Documentation (`docs/`)

- **`TEMPORAL_ENHANCEMENTS_SUMMARY.md`**: Detailed temporal features documentation
- **`PROJECT_STRUCTURE.md`**: This project structure guide

### 🚀 Examples (`examples/`)

- **`enhanced_system_demo.py`**: Comprehensive demonstration script
  - Temporal data specificity examples
  - Complete document retrieval demos
  - Multi-agent orchestration examples

## Key Features

### ✅ Enhanced Temporal Capabilities
- **Quarter-specific analysis**: "2024-Q3 vs 2023-Q3"
- **Annual comparisons**: "2024 vs 2023"
- **Multi-period trends**: Historical analysis across quarters/years
- **Date range filtering**: SEC filings within specific periods

### ✅ Complete Document Analysis
- **Full SEC document retrieval**: Complete 10-K/10-Q content
- **Section-specific extraction**: Business, Risk Factors, MD&A
- **Source attribution**: Filing dates, accession numbers
- **Metadata preservation**: Document context and references

### ✅ Multi-Agent Orchestration
- **5 specialized agent categories**: Comprehensive financial analysis
- **Function calling coordination**: Enhanced tool interaction
- **Cost tracking**: Detailed monitoring of AI usage and costs
- **Comprehensive logging**: Full audit trail of all interactions

## Configuration Files

- **`config.py`**: Centralized configuration management
- **`requirements.txt`**: Python package dependencies
- **`pyproject.toml`**: Project metadata and build configuration
- **`Dockerfile`**: Container configuration for deployment
- **`docker-compose.yml`**: Multi-container orchestration

## Interface

- **`cli.py`**: Command-line interface with enhanced features
  - Interactive mode
  - Direct query execution
  - Temporal analysis support
  - Multi-agent coordination

## Deployment

- **Docker support**: Complete containerization
- **Environment configuration**: Flexible deployment options
- **AWS integration**: Seamless Bedrock connectivity
- **Logging and monitoring**: Production-ready observability

## Removed Components

The following components were removed during cleanup:
- ❌ Test files (`test_*.py`)
- ❌ Cache directories (`__pycache__/`)
- ❌ Temporary log files
- ❌ Duplicate documentation files
- ❌ Empty directories
- ❌ Development artifacts

## Usage

See the main [README.md](../README.md) for comprehensive usage instructions, including:
- Installation and setup
- Configuration requirements
- Usage examples
- API reference
- Deployment instructions
