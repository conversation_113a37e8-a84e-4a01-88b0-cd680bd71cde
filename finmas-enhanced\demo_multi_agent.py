#!/usr/bin/env python3
"""
Multi-Agent Architecture Demonstration

This script demonstrates the enhanced multi-agent financial analysis system
with different collaboration modes and specialized agents.
"""

import asyncio
import sys
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from config import Config
from src.bedrock_client import BedrockClient
from src.data_sources import FinancialDataProvider
from src.agents import AgentCoordinator
from utils.logging_utils import setup_logging


class MultiAgentDemo:
    """Demonstration of multi-agent financial analysis capabilities"""
    
    def __init__(self):
        self.config = Config()
        self.logger = setup_logging()
        self.bedrock_client = None
        self.data_provider = None
        self.agent_coordinator = None
    
    async def initialize(self):
        """Initialize the demo system"""
        print("🚀 Initializing Multi-Agent Financial Analysis System")
        print("=" * 60)
        
        try:
            # Initialize components (mock mode for demo)
            print("📡 Setting up AWS Bedrock client...")
            self.bedrock_client = BedrockClient(self.config)
            
            print("📊 Initializing financial data provider...")
            self.data_provider = FinancialDataProvider(self.config)
            
            print("🤖 Setting up agent coordinator...")
            self.agent_coordinator = AgentCoordinator(self.bedrock_client, self.data_provider)
            
            print("✅ System initialized successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Initialization failed: {str(e)}")
            print("💡 Note: This demo requires AWS credentials for full functionality")
            return False
    
    def demonstrate_agent_types(self):
        """Demonstrate the different agent types"""
        print("\n🤖 Available Financial Analysis Agents")
        print("=" * 60)
        
        agents_info = {
            "Technical Analyst": {
                "specialty": "Chart patterns, indicators, price action",
                "focus": "RSI, MACD, moving averages, support/resistance",
                "output": "Entry/exit points, price targets, risk levels"
            },
            "Fundamental Analyst": {
                "specialty": "Financial statements, valuation, business analysis",
                "focus": "Revenue, margins, ratios, growth prospects",
                "output": "Fair value estimates, investment thesis, financial health"
            },
            "News Analyst": {
                "specialty": "Sentiment analysis, catalyst identification",
                "focus": "Recent headlines, market sentiment, events",
                "output": "Sentiment scores, catalysts, risk factors"
            },
            "Market Analyst": {
                "specialty": "Sector analysis, market dynamics",
                "focus": "Industry positioning, peer comparison, correlations",
                "output": "Market context, relative performance, sector trends"
            },
            "Risk Analyst": {
                "specialty": "Risk assessment and management",
                "focus": "Volatility, downside scenarios, risk metrics",
                "output": "Risk scores, mitigation strategies, position sizing"
            },
            "Investment Advisor": {
                "specialty": "Synthesis and recommendations",
                "focus": "Integrating all analysis into actionable advice",
                "output": "Buy/sell/hold ratings, price targets, strategy"
            }
        }
        
        for agent_name, info in agents_info.items():
            print(f"\n📋 {agent_name}")
            print(f"   Specialty: {info['specialty']}")
            print(f"   Focus: {info['focus']}")
            print(f"   Output: {info['output']}")
    
    def demonstrate_collaboration_modes(self):
        """Demonstrate different collaboration modes"""
        print("\n🔄 Agent Collaboration Modes")
        print("=" * 60)
        
        modes = {
            "Parallel": {
                "description": "Agents work simultaneously for speed",
                "use_case": "Quick analysis, simple queries",
                "benefits": "Fastest execution, independent analysis",
                "example": "Basic stock lookup or comparison"
            },
            "Sequential": {
                "description": "Agents work in order, sharing context",
                "use_case": "Focused analysis with building context",
                "benefits": "Context sharing, progressive insights",
                "example": "Technical analysis followed by risk assessment"
            },
            "Hierarchical": {
                "description": "Multi-phase analysis with dependencies",
                "use_case": "Comprehensive analysis requiring synthesis",
                "benefits": "Structured workflow, expert synthesis",
                "example": "Full investment analysis with final recommendation"
            }
        }
        
        for mode_name, info in modes.items():
            print(f"\n🔧 {mode_name} Mode")
            print(f"   Description: {info['description']}")
            print(f"   Use Case: {info['use_case']}")
            print(f"   Benefits: {info['benefits']}")
            print(f"   Example: {info['example']}")
    
    async def demonstrate_analysis_workflow(self, ticker: str = "AAPL"):
        """Demonstrate a complete analysis workflow"""
        print(f"\n📈 Multi-Agent Analysis Workflow for {ticker}")
        print("=" * 60)
        
        if not self.agent_coordinator:
            print("❌ System not initialized - running in demo mode")
            self._simulate_workflow(ticker)
            return
        
        try:
            print("🔍 Phase 1: Data Gathering Agents (Parallel)")
            print("   - Technical Analyst: Analyzing price patterns...")
            print("   - Fundamental Analyst: Reviewing financial statements...")
            print("   - News Analyst: Scanning recent headlines...")
            print("   - Market Analyst: Assessing sector context...")
            
            # Simulate parallel execution
            await asyncio.sleep(1)
            print("   ✅ Data gathering complete")
            
            print("\n🧮 Phase 2: Risk Assessment (Sequential)")
            print("   - Risk Analyst: Evaluating volatility and downside...")
            print("   - Incorporating insights from data gathering agents...")
            
            await asyncio.sleep(0.5)
            print("   ✅ Risk assessment complete")
            
            print("\n🎯 Phase 3: Investment Synthesis (Hierarchical)")
            print("   - Investment Advisor: Synthesizing all analysis...")
            print("   - Resolving conflicting viewpoints...")
            print("   - Generating final recommendation...")
            
            await asyncio.sleep(0.5)
            print("   ✅ Investment recommendation complete")
            
            print(f"\n📊 Analysis Summary for {ticker}:")
            print("   - 6 specialized agents executed")
            print("   - Hierarchical collaboration mode used")
            print("   - Comprehensive investment thesis generated")
            print("   - Risk-adjusted recommendations provided")
            
        except Exception as e:
            print(f"❌ Workflow demonstration failed: {str(e)}")
            self._simulate_workflow(ticker)
    
    def _simulate_workflow(self, ticker: str):
        """Simulate workflow for demo purposes"""
        print(f"🎭 Simulating multi-agent analysis for {ticker}...")
        print("   ✅ Technical analysis: Bullish momentum, RSI 65.2")
        print("   ✅ Fundamental analysis: Strong financials, P/E 30.95x")
        print("   ✅ News analysis: Positive sentiment, 7.2/10 score")
        print("   ✅ Market analysis: Outperforming sector, beta 1.2")
        print("   ✅ Risk analysis: Moderate risk, good risk/reward")
        print("   ✅ Investment synthesis: BUY rating, $215 target")
    
    def demonstrate_key_features(self):
        """Demonstrate key features of the multi-agent system"""
        print("\n⭐ Key Features of Enhanced Multi-Agent Architecture")
        print("=" * 60)
        
        features = [
            "🧠 LLM-Driven Intelligence: No hardcoded patterns, pure AI reasoning",
            "🤝 Agent Collaboration: Agents share insights and build on each other's work",
            "🔄 Dynamic Workflows: Collaboration mode adapts to query complexity",
            "📊 Real-time Data: Live market data, technical indicators, news feeds",
            "💰 Cost Optimization: Efficient execution with comprehensive cost tracking",
            "🎯 Specialized Expertise: Each agent focuses on their domain of expertise",
            "🔧 Function Calling: Native AWS Bedrock function calling integration",
            "📈 Synthesis Engine: Investment advisor synthesizes all perspectives",
            "⚡ Scalable Architecture: Easy to add new agents and capabilities",
            "🛡️ Risk Management: Dedicated risk analysis and mitigation strategies"
        ]
        
        for feature in features:
            print(f"   {feature}")
    
    async def run_demo(self):
        """Run the complete demonstration"""
        print("🎯 FinMAS Enhanced - Multi-Agent Architecture Demo")
        print("=" * 80)
        
        # Initialize system
        initialized = await self.initialize()
        
        # Demonstrate components
        self.demonstrate_agent_types()
        self.demonstrate_collaboration_modes()
        self.demonstrate_key_features()
        
        # Demonstrate workflow
        await self.demonstrate_analysis_workflow("AAPL")
        
        # Summary
        print("\n🎉 Multi-Agent Architecture Demonstration Complete!")
        print("=" * 60)
        
        if initialized:
            print("✅ System is ready for live financial analysis")
            print("💡 Run 'python cli.py' to start the interactive CLI")
        else:
            print("⚠️ Set up AWS credentials for full functionality")
            print("💡 See README.md for setup instructions")
        
        print("\n🚀 Next Steps:")
        print("   1. Configure AWS Bedrock access")
        print("   2. Set environment variables")
        print("   3. Run the CLI for interactive analysis")
        print("   4. Try queries like 'Analyze Apple stock'")


async def main():
    """Main demo runner"""
    demo = MultiAgentDemo()
    await demo.run_demo()


if __name__ == "__main__":
    asyncio.run(main())
