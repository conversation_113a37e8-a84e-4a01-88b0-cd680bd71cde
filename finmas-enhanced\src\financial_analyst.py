"""
Main Financial Analyst Orchestrator

This module coordinates all agents and provides the main interface
for the command-line financial analysis system.
"""

import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from config import Config
from .bedrock_client import BedrockClient
from .data_sources import FinancialDataProvider
from .intent_analyzer import IntentAnalyzer, QueryIntent
from .agents import (TechnicalAnalyst, FundamentalAnalyst, NewsAnalyst, InvestmentAdvisor,
                     MarketAnalyst, RiskAnalyst, AgentCoordinator, AgentResult)
from .agent_communication import SmartAgentOrchestrator
from .function_calling import FunctionCallOrchestrator
from utils.logging_utils import get_logger


class FinancialAnalyst:
    """Main financial analyst that orchestrates all components"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger()
        
        # Core components
        self.bedrock_client: Optional[BedrockClient] = None
        self.data_provider: Optional[FinancialDataProvider] = None
        self.intent_analyzer: Optional[IntentAnalyzer] = None
        self.function_orchestrator: Optional[FunctionCallOrchestrator] = None
        
        # Agent Coordinator and Smart Orchestrator
        self.agent_coordinator: Optional[AgentCoordinator] = None
        self.smart_orchestrator: Optional[SmartAgentOrchestrator] = None
        
        # Conversation context
        self.conversation_history: List[Dict[str, Any]] = []
        
        self.logger.logger.info("Financial Analyst initialized")
    
    async def initialize(self):
        """Initialize all components"""
        try:
            # Initialize Bedrock client
            self.bedrock_client = BedrockClient(self.config)
            
            # Test Bedrock connection
            if not await self.bedrock_client.health_check():
                raise Exception("Bedrock health check failed")
            
            # Initialize data provider
            self.data_provider = FinancialDataProvider(self.config)
            
            # Initialize intent analyzer
            self.intent_analyzer = IntentAnalyzer(self.bedrock_client)
            
            # Initialize function orchestrator
            self.function_orchestrator = FunctionCallOrchestrator(
                self.bedrock_client, self.data_provider
            )
            
            # Initialize agent coordinator and smart orchestrator
            self.agent_coordinator = AgentCoordinator(self.bedrock_client, self.data_provider)
            self.smart_orchestrator = SmartAgentOrchestrator(self.agent_coordinator.agents)
            
            self.logger.logger.info("All components initialized successfully")
            
        except Exception as e:
            self.logger.logger.error(f"Initialization failed: {str(e)}")
            raise
    
    async def analyze_query(self, query: str) -> Tuple[str, Dict[str, Any]]:
        """
        Analyze a user query and provide comprehensive response
        
        Args:
            query: Natural language query from user
            
        Returns:
            Tuple of (response_text, cost_info)
        """
        try:
            self.logger.logger.info(f"Analyzing query: {query[:100]}...")
            
            # Add to conversation history
            self.conversation_history.append({
                "role": "user",
                "content": query,
                "timestamp": datetime.now().isoformat()
            })
            
            # Analyze intent
            intent = await self.intent_analyzer.analyze_query(query)
            
            self.logger.logger.info(
                f"Intent analysis: {intent.primary_intent}, tickers: {intent.tickers}, "
                f"confidence: {intent.confidence}"
            )
            
            # Validate tickers
            if intent.tickers:
                validated_tickers = await self.intent_analyzer.validate_tickers(intent.tickers)
                intent.tickers = validated_tickers
            
            if not intent.tickers:
                return self._handle_no_tickers(query, intent)
            
            # Execute analysis based on intent
            if intent.comparison_mode and len(intent.tickers) > 1:
                response, cost_info = await self._handle_comparison(intent)
            else:
                response, cost_info = await self._handle_single_stock_analysis(intent)
            
            # Add to conversation history
            self.conversation_history.append({
                "role": "assistant",
                "content": response,
                "timestamp": datetime.now().isoformat(),
                "cost_info": cost_info
            })
            
            return response, cost_info
            
        except Exception as e:
            error_message = f"I apologize, but I encountered an error analyzing your query: {str(e)}"
            self.logger.logger.error(f"Query analysis failed: {str(e)}")
            return error_message, {"total_cost": 0, "error": str(e)}
    
    async def _handle_single_stock_analysis(self, intent: QueryIntent) -> Tuple[str, Dict[str, Any]]:
        """Handle analysis for a single stock using smart agent orchestration"""
        ticker = intent.tickers[0]

        async with self.data_provider:
            # Use smart orchestrator for dynamic agent communication
            agent_results = await self.smart_orchestrator.analyze_query(
                ticker=ticker,
                user_query=self.conversation_history[-1]['content'] if self.conversation_history else "",
                query_intent=intent
            )

            # Get summary statistics
            summary = self.agent_coordinator.get_agent_summary(agent_results)

            # Synthesize results
            synthesis_focus = self._determine_synthesis_focus(intent)
            response = await self.agent_coordinator.synthesize_results(
                ticker=ticker,
                agent_results=agent_results,
                synthesis_focus=synthesis_focus
            )

        # Prepare cost information
        cost_info = {
            "total_cost": summary.get("total_cost", 0),
            "agents_executed": summary.get("agents_executed", 0),
            "average_confidence": summary.get("average_confidence", 0),
            "duration_ms": summary.get("total_execution_time_ms", 0),
            "orchestration_mode": "smart_dynamic",
            "synthesis_focus": synthesis_focus
        }

        return response, cost_info
    
    async def _handle_comparison(self, intent: QueryIntent) -> Tuple[str, Dict[str, Any]]:
        """Handle stock comparison analysis using enhanced multi-agent system"""
        tickers = intent.tickers[:3]  # Limit to 3 stocks for comparison

        # Use focused agents for comparison
        comparison_agents = ["technical", "fundamental", "market"]

        # Run analysis for each stock
        all_results = {}
        total_cost = 0.0
        total_agents = 0

        async with self.data_provider:
            for ticker in tickers:
                # Run parallel analysis for speed in comparison
                stock_results = await self.agent_coordinator.run_collaborative_analysis(
                    ticker=ticker,
                    agent_types=comparison_agents,
                    collaboration_mode="parallel"
                )

                all_results[ticker] = stock_results

                # Update totals
                summary = self.agent_coordinator.get_agent_summary(stock_results)
                total_cost += summary.get("total_cost", 0)
                total_agents += summary.get("agents_executed", 0)

        # Generate comparison response
        response = await self._synthesize_comparison_response(intent, all_results)

        cost_info = {
            "total_cost": total_cost,
            "stocks_compared": len(tickers),
            "agents_executed": total_agents,
            "comparison_mode": "multi_agent_parallel"
        }

        return response, cost_info
    
    def _determine_agents(self, intent: QueryIntent) -> List[str]:
        """Determine which agents to run based on intent"""
        agents = []

        if intent.primary_intent == "technical" or "technical" in intent.analysis_types:
            agents.append("technical")

        if intent.primary_intent == "fundamental" or "fundamental" in intent.analysis_types:
            agents.append("fundamental")

        if intent.primary_intent == "news" or "news" in intent.analysis_types:
            agents.append("news")

        if intent.primary_intent == "recommendation" or "recommendation" in intent.analysis_types:
            agents.append("investment")

        # Add market analysis for context
        if intent.primary_intent in ["analyze", "comprehensive"] or "comprehensive" in intent.analysis_types:
            agents.extend(["market", "risk"])

        # Default to comprehensive analysis
        if not agents or "comprehensive" in intent.analysis_types:
            agents = ["technical", "fundamental", "news", "market", "risk"]

        # Always include investment advisor for synthesis unless specifically excluded
        if "investment" not in agents and len(agents) > 1:
            agents.append("investment")

        return agents

    def _determine_collaboration_mode(self, intent: QueryIntent, agents: List[str]) -> str:
        """Determine the best collaboration mode based on intent and agents"""

        # For simple queries, use parallel execution
        if len(agents) <= 2:
            return "parallel"

        # For comprehensive analysis, use hierarchical
        if intent.primary_intent in ["analyze", "comprehensive"] or "comprehensive" in intent.analysis_types:
            return "hierarchical"

        # For specific analysis types, use sequential
        if intent.primary_intent in ["technical", "fundamental", "news"]:
            return "sequential"

        # Default to hierarchical for complex analysis
        return "hierarchical"

    def _determine_synthesis_focus(self, intent: QueryIntent) -> str:
        """Determine synthesis focus based on user intent"""

        if intent.primary_intent == "recommendation" or "recommendation" in intent.analysis_types:
            return "investment"

        if intent.primary_intent == "technical":
            return "technical"

        if "risk" in intent.specific_metrics or any("risk" in metric.lower() for metric in intent.specific_metrics):
            return "risk"

        # Default to comprehensive
        return "comprehensive"
    
    async def _synthesize_response(
        self, 
        ticker: str, 
        intent: QueryIntent, 
        agent_results: Dict[str, AgentResult]
    ) -> str:
        """Synthesize agent results into a comprehensive response"""
        
        # Prepare synthesis prompt
        synthesis_prompt = f"""Synthesize the following financial analysis for {ticker} into a comprehensive, conversational response:

USER QUERY INTENT: {intent.primary_intent}
ANALYSIS TYPES REQUESTED: {intent.analysis_types}

AGENT ANALYSIS RESULTS:
"""
        
        for agent_name, result in agent_results.items():
            if isinstance(result, AgentResult) and result.analysis:
                synthesis_prompt += f"\n{agent_name.upper()} ANALYSIS:\n{result.analysis}\n"
        
        synthesis_prompt += """
Please provide a comprehensive response that:
1. Directly answers the user's question
2. Synthesizes insights from all available analysis
3. Provides specific data points and metrics
4. Includes actionable recommendations
5. Maintains a conversational, professional tone
6. Highlights key risks and opportunities

Format the response as a natural conversation with the user, not as a formal report."""
        
        # Get synthesis from LLM
        messages = [{"role": "user", "content": [{"text": synthesis_prompt}]}]
        
        system_message = """You are an expert financial analyst providing personalized investment advice. 
        Synthesize complex financial analysis into clear, actionable insights. Be conversational but professional, 
        and always provide specific data points and reasoning for your recommendations."""
        
        response, _ = await self.bedrock_client.converse_async(
            messages=messages,
            system_message=system_message,
            max_tokens=3000,
            temperature=0.2
        )
        
        # Extract response text
        response_text = ""
        output_message = response.get('output', {}).get('message', {})
        content = output_message.get('content', [])
        
        for content_block in content:
            if 'text' in content_block:
                response_text += content_block['text']
        
        return response_text
    
    async def _synthesize_comparison_response(
        self, 
        intent: QueryIntent, 
        all_results: Dict[str, Dict[str, AgentResult]]
    ) -> str:
        """Synthesize comparison analysis"""
        
        comparison_prompt = f"""Compare the following stocks based on the analysis results:

STOCKS BEING COMPARED: {', '.join(intent.tickers)}

ANALYSIS RESULTS:
"""
        
        for ticker, results in all_results.items():
            comparison_prompt += f"\n{ticker} ANALYSIS:\n"
            for agent_name, result in results.items():
                if isinstance(result, AgentResult) and result.analysis:
                    comparison_prompt += f"{agent_name}: {result.analysis[:500]}...\n"
        
        comparison_prompt += """
Provide a comprehensive comparison that:
1. Compares key metrics across all stocks
2. Identifies the strengths and weaknesses of each
3. Provides a clear ranking or recommendation
4. Explains the reasoning behind the comparison
5. Considers different investment objectives

Be specific and actionable in your comparison."""
        
        # Get comparison from LLM
        messages = [{"role": "user", "content": [{"text": comparison_prompt}]}]
        
        system_message = """You are an expert financial analyst comparing stocks. Provide clear, 
        data-driven comparisons with specific recommendations for different types of investors."""
        
        response, _ = await self.bedrock_client.converse_async(
            messages=messages,
            system_message=system_message,
            max_tokens=2500,
            temperature=0.2
        )
        
        # Extract response text
        response_text = ""
        output_message = response.get('output', {}).get('message', {})
        content = output_message.get('content', [])
        
        for content_block in content:
            if 'text' in content_block:
                response_text += content_block['text']
        
        return response_text
    
    def _handle_no_tickers(self, query: str, intent: QueryIntent) -> Tuple[str, Dict[str, Any]]:
        """Handle queries with no valid tickers"""
        response = f"""I understand you're asking about {intent.primary_intent}, but I couldn't identify any specific stock ticker symbols in your query.

Could you please specify which stock(s) you'd like me to analyze? For example:
- "Analyze Apple stock" (AAPL)
- "What's the technical outlook for Microsoft?" (MSFT)
- "Compare Tesla vs Ford" (TSLA vs F)

I can analyze any publicly traded stock using its ticker symbol."""
        
        return response, {"total_cost": 0, "no_tickers": True}
    
    def clear_conversation(self):
        """Clear conversation history"""
        self.conversation_history = []
        self.logger.logger.info("Conversation history cleared")
    
    def cleanup(self):
        """Cleanup resources"""
        self.logger.logger.info("Cleaning up Financial Analyst resources")
        # Any cleanup needed for async resources
