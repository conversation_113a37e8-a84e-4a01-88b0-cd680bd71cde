#!/usr/bin/env python3
"""
Test script to verify enhanced logging with API calls and AI prompts
"""

import sys
import asyncio
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from utils.logging_utils import get_logger, reset_logger, check_logger_status
from src.data_sources import FinancialDataProvider
from src.bedrock_client import BedrockClient
from config import Config


async def test_enhanced_logging():
    """Test enhanced logging with API calls and AI prompts"""
    print("🔍 Testing Enhanced Logging System")
    print("=" * 60)
    
    # Reset logger to start fresh
    reset_logger()
    
    # Initialize logger
    logger = get_logger()
    
    print(f"\n📁 Log files created:")
    print(f"   Main log: {logger.log_file}")
    print(f"   API calls log: {logger.api_calls_log_file}")
    print(f"   AI prompts log: {logger.ai_prompts_log_file}")
    
    # Test 1: Manual API call logging
    print("\n1. Testing manual API call logging...")
    logger.log_api_call(
        api_name="test_api",
        endpoint="/test/endpoint",
        method="GET",
        parameters={"param1": "value1", "param2": 123},
        response_summary="Test response with 5 fields",
        execution_time_ms=150.5,
        success=True,
        status_code=200
    )
    print("   ✅ Manual API call logged")
    
    # Test 2: Manual AI prompt logging
    print("\n2. Testing manual AI prompt logging...")
    logger.log_ai_prompt(
        model="test-model",
        system_prompt="You are a helpful assistant.",
        user_prompt="What is the capital of France?",
        response="The capital of France is Paris.",
        input_tokens=15,
        output_tokens=8,
        cost=0.0023,
        execution_time_ms=1200.0,
        agent_name="test_agent",
        success=True
    )
    print("   ✅ Manual AI prompt logged")
    
    # Test 3: Real data provider API calls
    print("\n3. Testing real data provider API calls...")
    config = Config()
    data_provider = FinancialDataProvider(config)
    
    async with data_provider:
        # This should log API calls automatically
        stock_data = await data_provider.get_stock_data("AAPL")
        if stock_data:
            print(f"   ✅ Stock data retrieved: ${stock_data.current_price:.2f}")
        else:
            print("   ⚠️ No stock data retrieved")
    
    # Test 4: Check log file contents
    print("\n4. Checking log file contents...")
    
    # Check API calls log
    if logger.api_calls_log_file.exists():
        with open(logger.api_calls_log_file, 'r') as f:
            api_log_content = f.read()
        api_call_count = api_log_content.count('"api_name"')
        print(f"   📊 API calls log: {api_call_count} calls logged")
        
        # Show sample API call log
        if api_call_count > 0:
            lines = api_log_content.strip().split('\n')
            if len(lines) > 0:
                print(f"   📝 Sample API log entry: {lines[0][:100]}...")
    else:
        print("   ❌ API calls log file not found")
    
    # Check AI prompts log
    if logger.ai_prompts_log_file.exists():
        with open(logger.ai_prompts_log_file, 'r') as f:
            ai_log_content = f.read()
        ai_call_count = ai_log_content.count('"model"')
        print(f"   🤖 AI prompts log: {ai_call_count} prompts logged")
        
        # Show sample AI prompt log
        if ai_call_count > 0:
            lines = ai_log_content.strip().split('\n')
            if len(lines) > 0:
                print(f"   📝 Sample AI log entry: {lines[0][:100]}...")
    else:
        print("   ❌ AI prompts log file not found")
    
    # Test 5: Session data export
    print("\n5. Testing session data export...")
    session_data = logger.export_session_data()
    
    print(f"   📈 Session summary:")
    print(f"      - Total AI calls: {session_data['summary_stats']['total_ai_calls']}")
    print(f"      - Total API calls: {session_data['summary_stats']['total_api_calls']}")
    print(f"      - Total function calls: {session_data['summary_stats']['total_function_calls']}")
    print(f"      - Total cost: ${session_data['summary_stats']['total_cost']:.4f}")
    print(f"      - Total tokens: {session_data['summary_stats']['total_tokens']}")
    
    # Test 6: Save session data
    print("\n6. Saving session data...")
    logger.save_session_data()
    
    session_json_file = logger.log_dir / f"session_data_{logger.session_id}.json"
    if session_json_file.exists():
        print(f"   ✅ Session data saved to: {session_json_file}")
        file_size = session_json_file.stat().st_size
        print(f"   📁 File size: {file_size} bytes")
    else:
        print("   ❌ Session data file not created")
    
    # Test 7: Logger status
    print("\n7. Final logger status...")
    status = check_logger_status()
    print(f"   📊 Handler count: {status['handler_count']}")
    print(f"   📊 Handler types: {status['handler_types']}")
    print(f"   📊 Global logger exists: {status['global_logger_exists']}")
    
    print("\n✅ Enhanced logging test completed!")
    
    # Return summary
    return {
        "api_calls_logged": len(logger.api_call_logs),
        "ai_prompts_logged": len(logger.ai_prompt_calls),
        "log_files_created": [
            logger.log_file.exists(),
            logger.api_calls_log_file.exists(),
            logger.ai_prompts_log_file.exists()
        ],
        "session_data_saved": session_json_file.exists() if 'session_json_file' in locals() else False
    }


async def test_bedrock_logging():
    """Test Bedrock client logging (if credentials available)"""
    print("\n🤖 Testing Bedrock AI Prompt Logging...")
    print("=" * 50)
    
    try:
        config = Config()
        bedrock_client = BedrockClient(config)
        
        # Simple test message
        test_messages = [{
            "role": "user",
            "content": [{"text": "Say 'Hello, logging test!' and nothing else."}]
        }]
        
        response, cost_info = await bedrock_client.converse_async(
            messages=test_messages,
            max_tokens=20,
            temperature=0.1
        )
        
        print("   ✅ Bedrock API call successful")
        print(f"   💰 Cost: ${cost_info['total_cost']:.4f}")
        print(f"   🔢 Tokens: {cost_info['input_tokens']}+{cost_info['output_tokens']}={cost_info['total_tokens']}")
        
        # Check if AI prompt was logged
        logger = get_logger()
        if logger.ai_prompt_calls:
            latest_call = logger.ai_prompt_calls[-1]
            print(f"   📝 AI prompt logged: {latest_call.model}")
            print(f"   📝 System prompt: {latest_call.system_prompt[:50]}...")
            print(f"   📝 User prompt: {latest_call.user_prompt[:50]}...")
            print(f"   📝 Response: {latest_call.response[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"   ⚠️ Bedrock test skipped: {str(e)}")
        return False


async def main():
    """Main test execution"""
    print("🚀 Enhanced Logging System Test Suite")
    print("=" * 60)
    
    # Test basic enhanced logging
    basic_results = await test_enhanced_logging()
    
    # Test Bedrock logging if possible
    bedrock_success = await test_bedrock_logging()
    
    # Final summary
    print("\n📊 Test Results Summary:")
    print(f"   API calls logged: {basic_results['api_calls_logged']}")
    print(f"   AI prompts logged: {basic_results['ai_prompts_logged']}")
    print(f"   Log files created: {sum(basic_results['log_files_created'])}/3")
    print(f"   Session data saved: {basic_results['session_data_saved']}")
    print(f"   Bedrock integration: {'✅' if bedrock_success else '⚠️'}")
    
    # Check log files exist
    logger = get_logger()
    print(f"\n📁 Log Files Location: {logger.log_dir}")
    print(f"   Main log: {logger.log_file.name}")
    print(f"   API calls: {logger.api_calls_log_file.name}")
    print(f"   AI prompts: {logger.ai_prompts_log_file.name}")
    
    success_rate = (
        basic_results['api_calls_logged'] > 0 and
        basic_results['ai_prompts_logged'] > 0 and
        sum(basic_results['log_files_created']) >= 2
    )
    
    if success_rate:
        print("\n🎉 Enhanced logging system is working correctly!")
    else:
        print("\n❌ Some logging features need attention")
    
    return success_rate


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
