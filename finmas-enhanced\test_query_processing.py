#!/usr/bin/env python3
"""
Test query processing without AWS credentials
"""

import asyncio
import sys
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from src.intent_analyzer import Intent<PERSON>nalyzer, QueryIntent


class MockBedrockClient:
    """Mock Bedrock client for testing"""
    
    async def converse_async(self, messages, system_message, max_tokens, temperature):
        """Mock converse method"""
        # Simulate the problematic response that was causing issues
        mock_response = {
            'output': {
                'message': {
                    'content': [{
                        'text': """{
    "primary_intent": "compare",
    "secondary_intents": ["analyze"],
    "tickers": ["AMZN", "MSFT", "META"],
    "confidence": 0.95,
    "analysis_types": ["fundamental", "comprehensive"],
    "timeframe": "medium",
    "comparison_mode": true,
    "specific_metrics": ["revenue", "growth", "market_performance"]
}

Note: While AWS is mentioned, it's a division of Amazon (AMZN), so I included AMZN as the relevant ticker."""
                    }]
                }
            }
        }
        
        cost_info = {
            "total_cost": 0.0043,
            "total_tokens": 770,
            "input_tokens": 603,
            "output_tokens": 167
        }
        
        return mock_response, cost_info


async def test_intent_analysis():
    """Test intent analysis with the problematic query"""
    print("🧪 Testing Intent Analysis with Mock Bedrock Client")
    print("=" * 60)
    
    # Create mock client and analyzer
    mock_client = MockBedrockClient()
    analyzer = IntentAnalyzer(mock_client)
    
    # Test the problematic query
    query = "how did aws performed vs microsoft and meta last year"
    
    print(f"📝 Query: {query}")
    print("⏳ Analyzing intent...")
    
    try:
        intent = await analyzer.analyze_query(query)
        
        print("✅ Intent analysis successful!")
        print(f"   Primary Intent: {intent.primary_intent}")
        print(f"   Tickers: {intent.tickers}")
        print(f"   Confidence: {intent.confidence}")
        print(f"   Comparison Mode: {intent.comparison_mode}")
        print(f"   Analysis Types: {intent.analysis_types}")
        print(f"   Timeframe: {intent.timeframe}")
        print(f"   Specific Metrics: {intent.specific_metrics}")
        
        # Validate the results
        expected_tickers = ["AMZN", "MSFT", "META"]
        if intent.primary_intent == "compare" and intent.comparison_mode and intent.tickers == expected_tickers:
            print("\n🎉 Intent analysis working correctly!")
            return True
        else:
            print("\n⚠️ Intent analysis results don't match expectations")
            return False
            
    except Exception as e:
        print(f"❌ Intent analysis failed: {str(e)}")
        return False


def test_fallback_analysis():
    """Test fallback analysis"""
    print("\n🔄 Testing Fallback Intent Analysis")
    print("=" * 60)
    
    # Create analyzer with mock client
    mock_client = MockBedrockClient()
    analyzer = IntentAnalyzer(mock_client)
    
    query = "how did aws performed vs microsoft and meta last year"
    
    print(f"📝 Query: {query}")
    print("⏳ Running fallback analysis...")
    
    try:
        intent = analyzer._fallback_intent_analysis(query)
        
        print("✅ Fallback analysis successful!")
        print(f"   Primary Intent: {intent.primary_intent}")
        print(f"   Tickers: {intent.tickers}")
        print(f"   Confidence: {intent.confidence}")
        print(f"   Comparison Mode: {intent.comparison_mode}")
        
        # Check if it correctly identified the companies
        expected_tickers = ["AMZN", "MSFT", "META"]
        if intent.primary_intent == "compare" and intent.comparison_mode:
            if all(ticker in intent.tickers for ticker in expected_tickers):
                print("\n🎉 Fallback analysis working correctly!")
                return True
            else:
                print(f"\n⚠️ Expected tickers {expected_tickers}, got {intent.tickers}")
                return False
        else:
            print("\n⚠️ Fallback analysis didn't detect comparison correctly")
            return False
            
    except Exception as e:
        print(f"❌ Fallback analysis failed: {str(e)}")
        return False


async def main():
    """Main test runner"""
    print("🔧 Query Processing Test Suite")
    print("=" * 80)
    
    # Test intent analysis with mock
    intent_test = await test_intent_analysis()
    
    # Test fallback analysis
    fallback_test = test_fallback_analysis()
    
    print("\n📊 Test Results:")
    print("=" * 40)
    print(f"Intent Analysis: {'✅ PASS' if intent_test else '❌ FAIL'}")
    print(f"Fallback Analysis: {'✅ PASS' if fallback_test else '❌ FAIL'}")
    
    if intent_test and fallback_test:
        print("\n🎉 All tests passed! Query processing should work correctly.")
        print("\n💡 The system can now handle:")
        print("   - Company name to ticker mapping (AWS → AMZN)")
        print("   - Comparison intent detection")
        print("   - JSON parsing with extra text")
        print("   - Robust fallback analysis")
    else:
        print("\n⚠️ Some tests failed. Check the implementation.")
    
    return intent_test and fallback_test


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
