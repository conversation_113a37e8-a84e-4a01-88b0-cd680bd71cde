# Enhanced Financial Analysis System (FinMAS)

## Overview

FinMAS Enhanced is a sophisticated multi-agent financial analysis system that leverages AWS Bedrock, SEC EDGAR data, and real-time market data to provide comprehensive financial insights with temporal specificity and complete document analysis capabilities.

## 🏗️ System Architecture

The system is built around 5 specialized agent categories that collaborate to provide comprehensive financial analysis:

### Core Agent Categories

1. **Financial Performance Analysis Agent** - Revenue growth, margin trends, EPS analysis
2. **Operational Metrics Agent** - Segment performance, geographic analysis, unit sales
3. **Risk Monitoring Agent** - Going concern, material weaknesses, litigation risks
4. **Valuation Analysis Agent** - Book value, insider activity, dividend analysis, ROIC/ROE
5. **Strategic Analysis Agent** - Growth drivers, competitive positioning, M&A analysis

### System Architecture Diagram

```mermaid
graph TB
    subgraph "User Interface Layer"
        CLI[CLI Interface]
        API[REST API]
    end
    
    subgraph "Orchestration Layer"
        FC[Function Call Orchestrator]
        IA[Intent Analyzer]
        AC[Agent Coordinator]
    end
    
    subgraph "Agent Layer"
        FPA[Financial Performance Agent]
        OMA[Operational Metrics Agent]
        RMA[Risk Monitoring Agent]
        VAA[Valuation Analysis Agent]
        SAA[Strategic Analysis Agent]
    end
    
    subgraph "Data Integration Layer"
        DP[Data Provider]
        SEC[SEC Data Provider]
        BC[Bedrock Client]
    end
    
    subgraph "External Services"
        AWS[AWS Bedrock]
        EDGAR[SEC EDGAR]
        YF[Yahoo Finance]
    end
    
    CLI --> FC
    API --> FC
    FC --> IA
    FC --> AC
    AC --> FPA
    AC --> OMA
    AC --> RMA
    AC --> VAA
    AC --> SAA
    
    FPA --> DP
    OMA --> DP
    RMA --> SEC
    VAA --> DP
    SAA --> SEC
    
    DP --> YF
    SEC --> EDGAR
    BC --> AWS
    
    FPA --> BC
    OMA --> BC
    RMA --> BC
    VAA --> BC
    SAA --> BC
```

## 🔧 Enhanced Features

### Temporal Data Specificity

The system now supports precise temporal queries with quarter and year specificity:

- **Quarter-specific analysis**: "2024-Q3 vs 2023-Q3"
- **Annual comparisons**: "2024 vs 2023"
- **Multi-period trends**: Revenue growth over last 3 years
- **Date range filtering**: SEC filings within specific periods

### Complete SEC Document Retrieval

Enhanced document analysis capabilities:

- **Full 10-K/10-Q content**: Complete document sections
- **Risk factors extraction**: Comprehensive risk analysis
- **Management Discussion & Analysis**: Full MD&A sections
- **Business descriptions**: Complete Item 1 business sections
- **Financial statements**: Detailed financial data with notes

## 🚀 Function Calling Workflow

```mermaid
sequenceDiagram
    participant User
    participant Orchestrator
    participant Agent
    participant DataProvider
    participant SEC
    participant Bedrock
    
    User->>Orchestrator: "Analyze MSFT CapEx for Q3 2024 vs Q3 2023"
    Orchestrator->>Orchestrator: Parse temporal intent
    Orchestrator->>Agent: get_temporal_financial_data()
    Agent->>DataProvider: get_temporal_financial_metrics("MSFT", "2024-Q3")
    DataProvider->>SEC: get_filing_by_period("MSFT", "2024-Q3")
    SEC-->>DataProvider: Filing data with period specificity
    DataProvider-->>Agent: Temporal financial metrics
    Agent->>Bedrock: Analyze with context
    Bedrock-->>Agent: Analysis response
    Agent-->>Orchestrator: Structured analysis
    Orchestrator-->>User: Comprehensive temporal analysis
```

## 📊 Data Source Integration

```mermaid
graph LR
    subgraph "Data Sources"
        SEC[SEC EDGAR API]
        YF[Yahoo Finance API]
        AWS[AWS Bedrock API]
    end
    
    subgraph "Data Providers"
        SDP[SEC Data Provider]
        FDP[Financial Data Provider]
        BC[Bedrock Client]
    end
    
    subgraph "Enhanced Capabilities"
        TD[Temporal Data]
        CD[Complete Documents]
        RT[Real-time Data]
    end
    
    SEC --> SDP
    YF --> FDP
    AWS --> BC
    
    SDP --> TD
    SDP --> CD
    FDP --> TD
    FDP --> RT
    BC --> RT
```

## 🛠️ Installation & Setup

### Prerequisites

- Python 3.11+
- AWS Account with Bedrock access
- Environment variables for API keys

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd finmas-enhanced

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
export AWS_ACCESS_KEY_ID="your-aws-access-key"
export AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
export AWS_DEFAULT_REGION="us-east-1"
```

### Configuration

Create a `.env` file:

```env
# AWS Bedrock Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_DEFAULT_REGION=us-east-1

# Optional API Keys for enhanced data
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-key
FINNHUB_API_KEY=your-finnhub-key
NEWS_API_KEY=your-news-api-key
```

## 📖 Usage Examples

### Temporal Data Analysis

```python
from src.function_calling import FunctionCallOrchestrator
from src.bedrock_client import BedrockClient
from src.data_sources import FinancialDataProvider
from config import Config

# Initialize system
config = Config()
bedrock_client = BedrockClient(config)

async with FinancialDataProvider(config) as data_provider:
    orchestrator = FunctionCallOrchestrator(bedrock_client, data_provider)
    
    # Temporal-specific query
    messages = [{
        "role": "user",
        "content": [{"text": "Analyze MSFT CapEx alignment with growth for Q3 2024 vs Q3 2023"}]
    }]
    
    response, function_calls, cost_info = await orchestrator.execute_with_functions(
        messages=messages,
        system_message="You are a financial analyst with temporal data access.",
        max_iterations=3
    )
```

### Complete Document Analysis

```python
# Complete SEC document retrieval
messages = [{
    "role": "user", 
    "content": [{"text": "Get complete 10-K for AAPL including all risk factors and MD&A"}]
}]

response, function_calls, cost_info = await orchestrator.execute_with_functions(
    messages=messages,
    system_message="Use complete document retrieval for comprehensive analysis.",
    max_iterations=3
)
```

### Multi-Agent Orchestration

```python
# Comprehensive multi-agent analysis
messages = [{
    "role": "user",
    "content": [{"text": "Perform comprehensive analysis of MSFT including financial performance, risks, and strategic position"}]
}]

response, function_calls, cost_info = await orchestrator.execute_with_functions(
    messages=messages,
    system_message="Coordinate multiple specialized agents for comprehensive analysis.",
    max_iterations=5
)
```

## 🔍 Enhanced Function Tools

### Temporal Financial Data

```python
{
    "name": "get_temporal_financial_data",
    "description": "Get financial data for specific time periods",
    "parameters": {
        "ticker": "MSFT",
        "period_type": "quarterly",
        "specific_period": "2024-Q3",
        "comparison_periods": ["2023-Q3", "2024-Q2"],
        "metrics": ["revenue", "capex", "operating_income"]
    }
}
```

### Complete SEC Document

```python
{
    "name": "get_complete_sec_document",
    "description": "Retrieve complete SEC document with full content",
    "parameters": {
        "ticker": "AAPL",
        "form_type": "10-K",
        "sections": ["business", "risk_factors", "mda", "financial_statements"],
        "include_exhibits": false
    }
}
```

### Enhanced SEC Filing Data

```python
{
    "name": "get_sec_filing_data",
    "description": "Get SEC filing data with temporal specificity",
    "parameters": {
        "ticker": "MSFT",
        "filing_date": "2024-Q3",
        "date_range": {"start_date": "2023-01-01", "end_date": "2024-12-31"},
        "full_document": true
    }
}
```

## 🔄 Agent Interaction Sequence

```mermaid
sequenceDiagram
    participant User
    participant CLI
    participant Orchestrator
    participant IntentAnalyzer
    participant FinancialAgent
    participant RiskAgent
    participant StrategicAgent
    participant DataProvider
    participant SEC
    participant Bedrock

    User->>CLI: "Comprehensive analysis of MSFT"
    CLI->>Orchestrator: Process query
    Orchestrator->>IntentAnalyzer: Analyze intent
    IntentAnalyzer-->>Orchestrator: Multi-agent analysis needed

    par Financial Analysis
        Orchestrator->>FinancialAgent: Analyze financial performance
        FinancialAgent->>DataProvider: Get temporal financial data
        DataProvider->>SEC: Retrieve Q3 2024 vs Q3 2023 data
        SEC-->>DataProvider: Financial metrics with periods
        DataProvider-->>FinancialAgent: Temporal data
        FinancialAgent->>Bedrock: Analyze with context
        Bedrock-->>FinancialAgent: Financial analysis
        FinancialAgent-->>Orchestrator: Financial results
    and Risk Analysis
        Orchestrator->>RiskAgent: Analyze risk factors
        RiskAgent->>SEC: Get complete 10-K document
        SEC-->>RiskAgent: Full risk factors section
        RiskAgent->>Bedrock: Analyze risks
        Bedrock-->>RiskAgent: Risk assessment
        RiskAgent-->>Orchestrator: Risk results
    and Strategic Analysis
        Orchestrator->>StrategicAgent: Analyze strategic position
        StrategicAgent->>SEC: Get MD&A and business sections
        SEC-->>StrategicAgent: Complete strategic content
        StrategicAgent->>Bedrock: Strategic analysis
        Bedrock-->>StrategicAgent: Strategic insights
        StrategicAgent-->>Orchestrator: Strategic results
    end

    Orchestrator->>Bedrock: Synthesize all analyses
    Bedrock-->>Orchestrator: Comprehensive report
    Orchestrator-->>CLI: Final analysis
    CLI-->>User: Complete financial analysis
```

## 🌊 Enhanced Temporal Data Retrieval Flow

```mermaid
flowchart TD
    A[User Query: "MSFT CapEx Q3 2024 vs Q3 2023"] --> B[Intent Analyzer]
    B --> C{Temporal Intent Detected?}
    C -->|Yes| D[Parse Period Specifications]
    C -->|No| E[Standard Data Retrieval]

    D --> F[Extract Periods: 2024-Q3, 2023-Q3]
    F --> G[Determine Form Types: 10-Q, 10-Q]
    G --> H[SEC Data Provider]

    H --> I[Get Filing by Period: 2024-Q3]
    H --> J[Get Filing by Period: 2023-Q3]

    I --> K[Extract CapEx from Q3 2024 10-Q]
    J --> L[Extract CapEx from Q3 2023 10-Q]

    K --> M[Temporal Financial Metrics]
    L --> M

    M --> N[Calculate Period Comparisons]
    N --> O[CapEx Growth Rate]
    N --> P[Revenue Alignment Analysis]

    O --> Q[Bedrock Analysis with Temporal Context]
    P --> Q

    Q --> R[Comprehensive Temporal Analysis]
    R --> S[User Response with Period Attribution]

    E --> T[Current/TTM Data Only]
    T --> U[Limited Temporal Context]
```

## 🖥️ CLI Usage

### Basic Commands

```bash
# Start interactive mode
python cli.py

# Direct query
python cli.py --query "Analyze MSFT financial performance for Q3 2024"

# Temporal analysis
python cli.py --query "Compare AAPL and MSFT CapEx alignment with growth 2023 vs 2024"

# Complete document analysis
python cli.py --query "Get complete 10-K analysis for TSLA including all risk factors"
```

### Advanced Usage

```bash
# Multi-agent orchestration
python cli.py --query "Comprehensive analysis of NVDA" --agents "financial,risk,strategic"

# Specific time periods
python cli.py --query "MSFT revenue trends Q1-Q3 2024" --temporal

# Export results
python cli.py --query "AAPL analysis" --output results.json
```

## 🐳 Docker Deployment

### Build and Run

```bash
# Build the container
docker build -t finmas-enhanced .

# Run with environment variables
docker run -e AWS_ACCESS_KEY_ID=your-key \
           -e AWS_SECRET_ACCESS_KEY=your-secret \
           -e AWS_DEFAULT_REGION=us-east-1 \
           finmas-enhanced

# Using docker-compose
docker-compose up
```

### Environment Configuration

```yaml
# docker-compose.yml
version: '3.8'
services:
  finmas:
    build: .
    environment:
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION}
    volumes:
      - ./logs:/app/logs
```

## 📋 API Reference

### Core Functions

| Function | Description | Parameters |
|----------|-------------|------------|
| `get_temporal_financial_data` | Period-specific financial metrics | ticker, period_type, specific_period, comparison_periods |
| `get_complete_sec_document` | Full SEC document retrieval | ticker, form_type, sections, include_exhibits |
| `get_sec_filing_data` | Enhanced SEC filing analysis | ticker, filing_date, date_range, full_document |
| `analyze_financial_performance` | Financial performance analysis | ticker, analysis_type |
| `analyze_risk_factors` | Risk monitoring and analysis | ticker, risk_category |
| `analyze_strategic_position` | Strategic analysis | ticker, strategic_focus |

### Response Format

```json
{
  "ticker": "MSFT",
  "analysis_type": "temporal_financial_data",
  "period_type": "quarterly",
  "temporal_data": {
    "2024-Q3": {
      "filing_date": "2024-10-24",
      "period_end_date": "2024-09-30",
      "metrics": {
        "revenue": 65300000000,
        "capex": 14900000000,
        "capex_to_revenue_ratio": 0.228
      }
    }
  },
  "data_source": "SEC EDGAR 10-Q Filing dated 2024-10-24",
  "timestamp": "2024-12-21T14:30:00Z"
}
```

## 🔧 Technical Architecture

### Directory Structure

```
finmas-enhanced/
├── src/                          # Core source code
│   ├── agents.py                 # Base agent classes
│   ├── specialized_agents.py     # 5 specialized agent categories
│   ├── risk_and_valuation_agents.py
│   ├── strategic_analysis_agent.py
│   ├── function_calling.py       # Enhanced function orchestration
│   ├── data_sources.py          # Temporal data providers
│   ├── sec_data_provider.py     # Complete SEC document retrieval
│   ├── bedrock_client.py        # AWS Bedrock integration
│   └── intent_analyzer.py       # Query intent analysis
├── utils/                        # Utility modules
│   ├── logging_utils.py         # Comprehensive logging
│   └── bedrock_utils.py         # Bedrock helper functions
├── docs/                         # Documentation
├── logs/                         # System logs
├── config.py                     # Configuration management
├── cli.py                        # Command-line interface
└── requirements.txt              # Dependencies
```

### Key Enhancements

1. **Temporal Specificity**: Quarter/year-specific data retrieval
2. **Complete Documents**: Full SEC filing analysis vs excerpts
3. **Enhanced Attribution**: Filing dates, accession numbers, source tracking
4. **Multi-Period Analysis**: Comparative analysis across time periods
5. **Function Orchestration**: Advanced tool coordination and workflow

## 📊 Performance & Monitoring

### Logging

The system provides comprehensive logging:

- **API Call Logs**: All AWS Bedrock interactions with cost tracking
- **AI Prompt Logs**: Complete prompt/response history
- **Session Logs**: User interaction tracking
- **Error Logs**: Detailed error tracking and debugging

### Cost Monitoring

```python
# Cost tracking example
response, function_calls, cost_info = await orchestrator.execute_with_functions(
    messages=messages,
    max_iterations=3
)

print(f"Total cost: ${cost_info['total_cost']:.4f}")
print(f"Function calls: {cost_info['function_calls']}")
print(f"Input tokens: {cost_info['input_tokens']}")
print(f"Output tokens: {cost_info['output_tokens']}")
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement enhancements
4. Add tests and documentation
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🔗 Related Documentation

- [Temporal Enhancements Summary](docs/TEMPORAL_ENHANCEMENTS_SUMMARY.md)
- [AWS Bedrock Documentation](https://docs.aws.amazon.com/bedrock/)
- [SEC EDGAR API](https://www.sec.gov/edgar)
- [edgartools Documentation](https://github.com/dgunning/edgartools)
```
```
