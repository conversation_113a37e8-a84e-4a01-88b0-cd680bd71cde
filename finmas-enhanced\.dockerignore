# FinMAS Enhanced Docker Ignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Data
data/
*.csv
*.json
*.parquet

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
docs/
*.md

# Tests
tests/
test_*.py
*_test.py

# Cache
.cache/
.pytest_cache/
.coverage
htmlcov/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Environment files
.env*
!.env.example

# Jupyter
.ipynb_checkpoints/
*.ipynb

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
