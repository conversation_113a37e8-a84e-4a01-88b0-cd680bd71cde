# FinMAS Enhanced Environment Configuration
# Copy this file to .env and fill in your values

# =============================================================================
# AWS CREDENTIALS (REQUIRED)
# =============================================================================
# Your AWS access credentials for Bedrock access
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_SESSION_TOKEN=your_session_token_here_if_using_temporary_credentials
AWS_DEFAULT_REGION=us-east-1

# =============================================================================
# BEDROCK CONFIGURATION
# =============================================================================
# Model to use for analysis (choose one):
# - us.anthropic.claude-3-5-sonnet-20241022-v2:0 (most capable, higher cost)
# - anthropic.claude-3-sonnet-20240229-v1:0 (balanced)
# - anthropic.claude-3-haiku-20240307-v1:0 (fastest, lowest cost)
BEDROCK_MODEL=us.anthropic.claude-3-5-sonnet-20241022-v2:0

# Model parameters
TEMPERATURE=0.1
MAX_TOKENS=4000

# =============================================================================
# COST MANAGEMENT
# =============================================================================
# Session budget limit in USD
BUDGET_LIMIT=10.0

# =============================================================================
# LOGGING
# =============================================================================
# Log level: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# =============================================================================
# OPTIONAL: ENHANCED DATA SOURCES
# =============================================================================
# These are optional but provide richer data
# Sign up at respective services to get API keys

# Alpha Vantage (financial data)
# Get free key at: https://www.alphavantage.co/support/#api-key
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here

# Finnhub (financial data and news)
# Get free key at: https://finnhub.io/register
FINNHUB_API_KEY=your_finnhub_key_here

# News API (news data)
# Get free key at: https://newsapi.org/register
NEWS_API_KEY=your_news_api_key_here

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================
# These are used by docker-compose.yml
COMPOSE_PROJECT_NAME=finmas-enhanced
COMPOSE_FILE=docker-compose.yml
