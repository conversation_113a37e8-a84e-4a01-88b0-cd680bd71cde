"""
Real-time Financial Data Sources

This module provides dynamic access to real-time financial data
from multiple sources without hardcoded values.
"""

import asyncio
import aiohttp
import yfinance as yf
import pandas as pd
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
import json

from config import Config
from utils.logging_utils import get_logger


@dataclass
class StockData:
    """Represents comprehensive stock data"""
    ticker: str
    current_price: float
    change: float
    change_percent: float
    volume: int
    market_cap: Optional[float]
    pe_ratio: Optional[float]
    dividend_yield: Optional[float]
    fifty_two_week_high: Optional[float]
    fifty_two_week_low: Optional[float]
    beta: Optional[float]
    timestamp: str


@dataclass
class FinancialMetrics:
    """Represents financial metrics and ratios"""
    ticker: str
    revenue_ttm: Optional[float]
    revenue_growth: Optional[float]
    net_income: Optional[float]
    gross_margin: Optional[float]
    operating_margin: Optional[float]
    profit_margin: Optional[float]
    roe: Optional[float]
    roa: Optional[float]
    debt_to_equity: Optional[float]
    current_ratio: Optional[float]
    quick_ratio: Optional[float]
    free_cash_flow: Optional[float]
    book_value_per_share: Optional[float]
    earnings_per_share: Optional[float]
    timestamp: str


@dataclass
class TechnicalIndicators:
    """Represents technical analysis indicators"""
    ticker: str
    rsi: Optional[float]
    macd: Optional[Dict[str, float]]
    moving_averages: Optional[Dict[str, float]]
    bollinger_bands: Optional[Dict[str, float]]
    support_levels: Optional[List[float]]
    resistance_levels: Optional[List[float]]
    trend: Optional[str]
    momentum: Optional[str]
    timestamp: str


@dataclass
class NewsItem:
    """Represents a news article"""
    title: str
    summary: str
    url: str
    published_date: str
    source: str
    sentiment: Optional[str] = None


class FinancialDataProvider:
    """Unified financial data provider with multiple sources"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger()
        self.session: Optional[aiohttp.ClientSession] = None
        
        # API keys from environment
        self.alpha_vantage_key = config.data_sources.get('alpha_vantage')
        self.finnhub_key = config.data_sources.get('finnhub')
        self.news_api_key = config.data_sources.get('news_api')
        
        self.logger.logger.info("Financial data provider initialized")
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def get_stock_data(self, ticker: str) -> Optional[StockData]:
        """Get real-time stock data"""
        import time
        start_time = time.time()

        try:
            self.logger.logger.info(f"Fetching stock data for {ticker}")

            # Use yfinance for primary data (it's free and reliable)
            stock = yf.Ticker(ticker)
            info = stock.info

            execution_time_ms = (time.time() - start_time) * 1000

            # Log API call
            self.logger.log_api_call(
                api_name="yfinance",
                endpoint=f"ticker/{ticker}/info",
                method="GET",
                parameters={"ticker": ticker},
                response_summary=f"Stock info with {len(info)} fields" if info else "No data",
                execution_time_ms=execution_time_ms,
                success=bool(info and 'regularMarketPrice' in info)
            )

            if not info or 'regularMarketPrice' not in info:
                self.logger.logger.warning(f"No data found for ticker {ticker}")
                return None

            # Extract current price data
            current_price = info.get('regularMarketPrice', 0)
            previous_close = info.get('regularMarketPreviousClose', current_price)
            change = current_price - previous_close
            change_percent = (change / previous_close * 100) if previous_close else 0

            stock_data = StockData(
                ticker=ticker,
                current_price=current_price,
                change=change,
                change_percent=change_percent,
                volume=info.get('regularMarketVolume', 0),
                market_cap=info.get('marketCap'),
                pe_ratio=info.get('trailingPE'),
                dividend_yield=info.get('dividendYield'),
                fifty_two_week_high=info.get('fiftyTwoWeekHigh'),
                fifty_two_week_low=info.get('fiftyTwoWeekLow'),
                beta=info.get('beta'),
                timestamp=datetime.now().isoformat()
            )

            self.logger.logger.info(f"Stock data retrieved for {ticker}: ${current_price:.2f}")
            return stock_data

        except Exception as e:
            execution_time_ms = (time.time() - start_time) * 1000

            # Log failed API call
            self.logger.log_api_call(
                api_name="yfinance",
                endpoint=f"ticker/{ticker}/info",
                method="GET",
                parameters={"ticker": ticker},
                response_summary="",
                execution_time_ms=execution_time_ms,
                success=False,
                error_message=str(e)
            )

            self.logger.logger.error(f"Failed to get stock data for {ticker}: {str(e)}")
            return None
    
    async def get_financial_metrics(self, ticker: str) -> Optional[FinancialMetrics]:
        """Get comprehensive financial metrics"""
        try:
            self.logger.logger.info(f"Fetching financial metrics for {ticker}")

            stock = yf.Ticker(ticker)
            info = stock.info

            if not info:
                return None

            # Get financial statements
            try:
                financials = stock.financials
                balance_sheet = stock.balance_sheet
                cash_flow = stock.cashflow
            except:
                financials = balance_sheet = cash_flow = None

            # Calculate metrics from available data
            revenue_ttm = info.get('totalRevenue')
            revenue_growth = info.get('revenueGrowth')

            metrics = FinancialMetrics(
                ticker=ticker,
                revenue_ttm=revenue_ttm,
                revenue_growth=revenue_growth,
                net_income=info.get('netIncomeToCommon'),
                gross_margin=info.get('grossMargins'),
                operating_margin=info.get('operatingMargins'),
                profit_margin=info.get('profitMargins'),
                roe=info.get('returnOnEquity'),
                roa=info.get('returnOnAssets'),
                debt_to_equity=info.get('debtToEquity'),
                current_ratio=info.get('currentRatio'),
                quick_ratio=info.get('quickRatio'),
                free_cash_flow=info.get('freeCashflow'),
                book_value_per_share=info.get('bookValue'),
                earnings_per_share=info.get('trailingEps'),
                timestamp=datetime.now().isoformat()
            )

            self.logger.logger.info(f"Financial metrics retrieved for {ticker}")
            return metrics

        except Exception as e:
            self.logger.logger.error(f"Failed to get financial metrics for {ticker}: {str(e)}")
            return None

    async def get_temporal_financial_metrics(self, ticker: str, period: str) -> Optional[Dict[str, Any]]:
        """Get financial metrics for a specific time period"""
        try:
            self.logger.logger.info(f"Fetching temporal financial metrics for {ticker} - period: {period}")

            stock = yf.Ticker(ticker)

            # Get quarterly and annual financials
            quarterly_financials = stock.quarterly_financials
            annual_financials = stock.financials
            quarterly_balance_sheet = stock.quarterly_balance_sheet
            annual_balance_sheet = stock.balance_sheet
            quarterly_cashflow = stock.quarterly_cashflow
            annual_cashflow = stock.cashflow

            # Parse period (e.g., "2024-Q3", "2023")
            if 'Q' in period:
                year, quarter = period.split('-Q')
                is_quarterly = True
                target_year = int(year)
                target_quarter = int(quarter)
            else:
                target_year = int(period)
                is_quarterly = False

            temporal_metrics = {
                "ticker": ticker,
                "period": period,
                "period_type": "quarterly" if is_quarterly else "annual",
                "metrics": {},
                "timestamp": datetime.now().isoformat()
            }

            # Select appropriate data source
            if is_quarterly and quarterly_financials is not None and not quarterly_financials.empty:
                financials_df = quarterly_financials
                balance_df = quarterly_balance_sheet
                cashflow_df = quarterly_cashflow

                # Find the column that matches the target period
                target_column = None
                for col in financials_df.columns:
                    if col.year == target_year:
                        # Approximate quarter matching based on month
                        quarter_months = {1: [1, 2, 3], 2: [4, 5, 6], 3: [7, 8, 9], 4: [10, 11, 12]}
                        if col.month in quarter_months.get(target_quarter, []):
                            target_column = col
                            break

            else:
                # Use annual data
                financials_df = annual_financials
                balance_df = annual_balance_sheet
                cashflow_df = annual_cashflow

                # Find the column that matches the target year
                target_column = None
                if financials_df is not None and not financials_df.empty:
                    for col in financials_df.columns:
                        if col.year == target_year:
                            target_column = col
                            break

            if target_column is not None:
                # Extract financial metrics for the specific period
                if financials_df is not None and not financials_df.empty:
                    # Revenue metrics
                    if 'Total Revenue' in financials_df.index:
                        temporal_metrics["metrics"]["revenue"] = financials_df.loc['Total Revenue', target_column]
                    elif 'Revenue' in financials_df.index:
                        temporal_metrics["metrics"]["revenue"] = financials_df.loc['Revenue', target_column]

                    # Income metrics
                    if 'Net Income' in financials_df.index:
                        temporal_metrics["metrics"]["net_income"] = financials_df.loc['Net Income', target_column]

                    if 'Operating Income' in financials_df.index:
                        temporal_metrics["metrics"]["operating_income"] = financials_df.loc['Operating Income', target_column]

                    if 'Gross Profit' in financials_df.index:
                        temporal_metrics["metrics"]["gross_profit"] = financials_df.loc['Gross Profit', target_column]

                # Balance sheet metrics
                if balance_df is not None and not balance_df.empty and target_column in balance_df.columns:
                    if 'Total Assets' in balance_df.index:
                        temporal_metrics["metrics"]["total_assets"] = balance_df.loc['Total Assets', target_column]

                    if 'Total Debt' in balance_df.index:
                        temporal_metrics["metrics"]["total_debt"] = balance_df.loc['Total Debt', target_column]

                    if 'Stockholders Equity' in balance_df.index:
                        temporal_metrics["metrics"]["stockholders_equity"] = balance_df.loc['Stockholders Equity', target_column]

                # Cash flow metrics
                if cashflow_df is not None and not cashflow_df.empty and target_column in cashflow_df.columns:
                    if 'Free Cash Flow' in cashflow_df.index:
                        temporal_metrics["metrics"]["free_cash_flow"] = cashflow_df.loc['Free Cash Flow', target_column]

                    if 'Capital Expenditure' in cashflow_df.index:
                        temporal_metrics["metrics"]["capex"] = abs(cashflow_df.loc['Capital Expenditure', target_column])
                    elif 'Capital Expenditures' in cashflow_df.index:
                        temporal_metrics["metrics"]["capex"] = abs(cashflow_df.loc['Capital Expenditures', target_column])

                temporal_metrics["data_available"] = True
                temporal_metrics["period_date"] = target_column.isoformat() if hasattr(target_column, 'isoformat') else str(target_column)
            else:
                temporal_metrics["data_available"] = False
                temporal_metrics["error"] = f"No data found for period {period}"

            self.logger.logger.info(f"Temporal financial metrics retrieved for {ticker} - {period}")
            return temporal_metrics

        except Exception as e:
            self.logger.logger.error(f"Failed to get temporal financial metrics for {ticker} - {period}: {str(e)}")
            return {
                "ticker": ticker,
                "period": period,
                "data_available": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def get_technical_indicators(self, ticker: str, period: str = "1y") -> Optional[TechnicalIndicators]:
        """Calculate technical indicators from price data"""
        try:
            self.logger.logger.info(f"Calculating technical indicators for {ticker}")
            
            stock = yf.Ticker(ticker)
            hist = stock.history(period=period)
            
            if hist.empty:
                return None
            
            # Calculate technical indicators
            indicators = self._calculate_indicators(hist)
            
            technical_data = TechnicalIndicators(
                ticker=ticker,
                rsi=indicators.get('rsi'),
                macd=indicators.get('macd'),
                moving_averages=indicators.get('moving_averages'),
                bollinger_bands=indicators.get('bollinger_bands'),
                support_levels=indicators.get('support_levels'),
                resistance_levels=indicators.get('resistance_levels'),
                trend=indicators.get('trend'),
                momentum=indicators.get('momentum'),
                timestamp=datetime.now().isoformat()
            )
            
            self.logger.logger.info(f"Technical indicators calculated for {ticker}")
            return technical_data
            
        except Exception as e:
            self.logger.logger.error(f"Failed to calculate technical indicators for {ticker}: {str(e)}")
            return None
    
    def _calculate_indicators(self, hist: pd.DataFrame) -> Dict[str, Any]:
        """Calculate technical indicators from historical data"""
        indicators = {}
        
        try:
            # RSI calculation
            delta = hist['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            indicators['rsi'] = float(rsi.iloc[-1]) if not rsi.empty else None
            
            # Moving averages
            indicators['moving_averages'] = {
                'sma_20': float(hist['Close'].rolling(20).mean().iloc[-1]),
                'sma_50': float(hist['Close'].rolling(50).mean().iloc[-1]),
                'sma_200': float(hist['Close'].rolling(200).mean().iloc[-1]),
                'ema_12': float(hist['Close'].ewm(span=12).mean().iloc[-1]),
                'ema_26': float(hist['Close'].ewm(span=26).mean().iloc[-1])
            }
            
            # MACD
            ema_12 = hist['Close'].ewm(span=12).mean()
            ema_26 = hist['Close'].ewm(span=26).mean()
            macd_line = ema_12 - ema_26
            signal_line = macd_line.ewm(span=9).mean()
            histogram = macd_line - signal_line
            
            indicators['macd'] = {
                'macd': float(macd_line.iloc[-1]),
                'signal': float(signal_line.iloc[-1]),
                'histogram': float(histogram.iloc[-1])
            }
            
            # Bollinger Bands
            sma_20 = hist['Close'].rolling(20).mean()
            std_20 = hist['Close'].rolling(20).std()
            indicators['bollinger_bands'] = {
                'upper': float(sma_20.iloc[-1] + (std_20.iloc[-1] * 2)),
                'middle': float(sma_20.iloc[-1]),
                'lower': float(sma_20.iloc[-1] - (std_20.iloc[-1] * 2))
            }
            
            # Support and resistance levels (simplified)
            recent_data = hist.tail(50)
            highs = recent_data['High'].nlargest(3).tolist()
            lows = recent_data['Low'].nsmallest(3).tolist()
            
            indicators['resistance_levels'] = [float(h) for h in highs]
            indicators['support_levels'] = [float(l) for l in lows]
            
            # Trend analysis
            current_price = hist['Close'].iloc[-1]
            sma_50 = indicators['moving_averages']['sma_50']
            sma_200 = indicators['moving_averages']['sma_200']
            
            if current_price > sma_50 > sma_200:
                indicators['trend'] = 'bullish'
            elif current_price < sma_50 < sma_200:
                indicators['trend'] = 'bearish'
            else:
                indicators['trend'] = 'neutral'
            
            # Momentum
            rsi_val = indicators.get('rsi', 50)
            if rsi_val > 70:
                indicators['momentum'] = 'overbought'
            elif rsi_val < 30:
                indicators['momentum'] = 'oversold'
            else:
                indicators['momentum'] = 'neutral'
                
        except Exception as e:
            self.logger.logger.error(f"Error calculating indicators: {str(e)}")
        
        return indicators
    
    async def get_news(self, ticker: str, days_back: int = 7) -> List[NewsItem]:
        """Get recent news for a ticker"""
        try:
            self.logger.logger.info(f"Fetching news for {ticker}")
            
            # Use yfinance news (free)
            stock = yf.Ticker(ticker)
            news = stock.news
            
            news_items = []
            for item in news[:10]:  # Limit to 10 most recent
                news_item = NewsItem(
                    title=item.get('title', ''),
                    summary=item.get('summary', ''),
                    url=item.get('link', ''),
                    published_date=datetime.fromtimestamp(
                        item.get('providerPublishTime', 0)
                    ).isoformat(),
                    source=item.get('publisher', '')
                )
                news_items.append(news_item)
            
            self.logger.logger.info(f"Retrieved {len(news_items)} news items for {ticker}")
            return news_items
            
        except Exception as e:
            self.logger.logger.error(f"Failed to get news for {ticker}: {str(e)}")
            return []
    
    async def validate_ticker(self, ticker: str) -> bool:
        """Validate if a ticker symbol exists"""
        try:
            stock = yf.Ticker(ticker)
            info = stock.info
            return bool(info and 'regularMarketPrice' in info)
        except:
            return False
