#!/usr/bin/env python3
"""
Enhanced Financial Analysis System Demonstration

This script demonstrates the key features of the enhanced FinMAS system:
1. Temporal data specificity
2. Complete SEC document retrieval
3. Multi-agent orchestration
4. Enhanced function calling
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any

# Import system components
from src.function_calling import FunctionCallOrchestrator
from src.bedrock_client import BedrockClient
from src.data_sources import FinancialDataProvider
from config import Config
from utils.logging_utils import get_logger

logger = get_logger()

async def demo_temporal_data_specificity():
    """Demonstrate temporal data specificity features"""
    print("\n" + "="*80)
    print("🕒 TEMPORAL DATA SPECIFICITY DEMONSTRATION")
    print("="*80)
    
    config = Config()
    bedrock_client = BedrockClient(config)
    
    async with FinancialDataProvider(config) as data_provider:
        orchestrator = FunctionCallOrchestrator(bedrock_client, data_provider)
        
        # Example 1: Quarter-specific comparison
        print("\n1. Quarter-specific CapEx Analysis...")
        
        messages = [{
            "role": "user",
            "content": [{"text": "Analyze MSFT CapEx alignment with growth for Q3 2024 vs Q3 2023. Include specific filing dates and period attribution."}]
        }]
        
        system_message = """You are a financial analyst with access to temporal financial data. 
        Use get_temporal_financial_data to retrieve period-specific metrics.
        Always include filing dates, period end dates, and source attribution."""
        
        try:
            response, function_calls, cost_info = await orchestrator.execute_with_functions(
                messages=messages,
                system_message=system_message,
                max_iterations=3
            )
            
            print(f"✅ Analysis completed")
            print(f"📊 Function calls: {len(function_calls)}")
            print(f"💰 Cost: ${cost_info['total_cost']:.4f}")
            print(f"📝 Response preview: {response[:200]}...")
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")

async def demo_complete_document_retrieval():
    """Demonstrate complete SEC document retrieval"""
    print("\n" + "="*80)
    print("📄 COMPLETE DOCUMENT RETRIEVAL DEMONSTRATION")
    print("="*80)
    
    config = Config()
    bedrock_client = BedrockClient(config)
    
    async with FinancialDataProvider(config) as data_provider:
        orchestrator = FunctionCallOrchestrator(bedrock_client, data_provider)
        
        # Example 2: Complete 10-K analysis
        print("\n2. Complete 10-K Document Analysis...")
        
        messages = [{
            "role": "user",
            "content": [{"text": "Get complete 10-K analysis for AAPL including business description, all risk factors, and complete MD&A section."}]
        }]
        
        system_message = """You are a financial analyst with access to complete SEC documents.
        Use get_complete_sec_document to retrieve full document content.
        Include document metadata, filing dates, and section references."""
        
        try:
            response, function_calls, cost_info = await orchestrator.execute_with_functions(
                messages=messages,
                system_message=system_message,
                max_iterations=3
            )
            
            print(f"✅ Document analysis completed")
            print(f"📊 Function calls: {len(function_calls)}")
            print(f"💰 Cost: ${cost_info['total_cost']:.4f}")
            print(f"📝 Response preview: {response[:200]}...")
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")

async def demo_multi_agent_orchestration():
    """Demonstrate multi-agent orchestration"""
    print("\n" + "="*80)
    print("🤖 MULTI-AGENT ORCHESTRATION DEMONSTRATION")
    print("="*80)
    
    config = Config()
    bedrock_client = BedrockClient(config)
    
    async with FinancialDataProvider(config) as data_provider:
        orchestrator = FunctionCallOrchestrator(bedrock_client, data_provider)
        
        # Example 3: Comprehensive multi-agent analysis
        print("\n3. Comprehensive Multi-Agent Analysis...")
        
        messages = [{
            "role": "user",
            "content": [{"text": "Perform comprehensive analysis of NVDA including financial performance, operational metrics, risk assessment, valuation analysis, and strategic positioning."}]
        }]
        
        system_message = """You are coordinating multiple specialized financial analysis agents.
        Use all available agent functions to provide comprehensive analysis:
        - analyze_financial_performance
        - analyze_operational_metrics  
        - analyze_risk_factors
        - analyze_valuation_metrics
        - analyze_strategic_position
        
        Synthesize results from all agents into a cohesive analysis."""
        
        try:
            response, function_calls, cost_info = await orchestrator.execute_with_functions(
                messages=messages,
                system_message=system_message,
                max_iterations=5
            )
            
            print(f"✅ Multi-agent analysis completed")
            print(f"📊 Function calls: {len(function_calls)}")
            print(f"💰 Cost: ${cost_info['total_cost']:.4f}")
            print(f"📝 Response preview: {response[:200]}...")
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")

async def demo_enhanced_sec_filing_analysis():
    """Demonstrate enhanced SEC filing analysis with date ranges"""
    print("\n" + "="*80)
    print("📋 ENHANCED SEC FILING ANALYSIS DEMONSTRATION")
    print("="*80)
    
    config = Config()
    bedrock_client = BedrockClient(config)
    
    async with FinancialDataProvider(config) as data_provider:
        orchestrator = FunctionCallOrchestrator(bedrock_client, data_provider)
        
        # Example 4: Date range SEC analysis
        print("\n4. SEC Filing Analysis with Date Range...")
        
        messages = [{
            "role": "user",
            "content": [{"text": "Analyze SEC filings for TSLA from 2023 to 2024, focusing on CapEx trends and strategic initiatives. Include complete document analysis where relevant."}]
        }]
        
        system_message = """You are a financial analyst with enhanced SEC filing capabilities.
        Use get_sec_filing_data with temporal parameters:
        - Specify date ranges for comprehensive analysis
        - Use full_document=true for detailed analysis
        - Include filing dates and accession numbers
        - Provide source attribution for all data points."""
        
        try:
            response, function_calls, cost_info = await orchestrator.execute_with_functions(
                messages=messages,
                system_message=system_message,
                max_iterations=4
            )
            
            print(f"✅ Enhanced SEC analysis completed")
            print(f"📊 Function calls: {len(function_calls)}")
            print(f"💰 Cost: ${cost_info['total_cost']:.4f}")
            print(f"📝 Response preview: {response[:200]}...")
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")

async def demo_comparative_temporal_analysis():
    """Demonstrate comparative temporal analysis across companies"""
    print("\n" + "="*80)
    print("⚖️ COMPARATIVE TEMPORAL ANALYSIS DEMONSTRATION")
    print("="*80)
    
    config = Config()
    bedrock_client = BedrockClient(config)
    
    async with FinancialDataProvider(config) as data_provider:
        orchestrator = FunctionCallOrchestrator(bedrock_client, data_provider)
        
        # Example 5: Multi-company temporal comparison
        print("\n5. Multi-Company Temporal Comparison...")
        
        messages = [{
            "role": "user",
            "content": [{"text": "Compare MSFT, AAPL, and GOOGL CapEx alignment with revenue growth for 2023 vs 2024. Analyze quarterly trends and provide period-specific insights."}]
        }]
        
        system_message = """You are conducting comparative temporal financial analysis.
        Use temporal financial data and SEC filing analysis for multiple companies.
        Provide period-specific comparisons with exact dates and source attribution.
        Include quarterly trends and year-over-year analysis."""
        
        try:
            response, function_calls, cost_info = await orchestrator.execute_with_functions(
                messages=messages,
                system_message=system_message,
                max_iterations=6
            )
            
            print(f"✅ Comparative analysis completed")
            print(f"📊 Function calls: {len(function_calls)}")
            print(f"💰 Cost: ${cost_info['total_cost']:.4f}")
            print(f"📝 Response preview: {response[:200]}...")
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")

async def main():
    """Run all demonstrations"""
    print("🚀 ENHANCED FINANCIAL ANALYSIS SYSTEM DEMONSTRATION")
    print("=" * 80)
    print("Demonstrating key enhancements:")
    print("✅ Temporal data specificity with quarter/year precision")
    print("✅ Complete SEC document retrieval and analysis")
    print("✅ Multi-agent orchestration and coordination")
    print("✅ Enhanced function calling with temporal parameters")
    print("✅ Comparative analysis across time periods and companies")
    
    # Run all demonstrations
    await demo_temporal_data_specificity()
    await demo_complete_document_retrieval()
    await demo_multi_agent_orchestration()
    await demo_enhanced_sec_filing_analysis()
    await demo_comparative_temporal_analysis()
    
    print("\n" + "="*80)
    print("🎉 DEMONSTRATION COMPLETE")
    print("="*80)
    print("\nKey Features Demonstrated:")
    print("• Period-specific financial data (Q3 2024 vs Q3 2023)")
    print("• Complete SEC document analysis (full 10-K/10-Q content)")
    print("• Multi-agent coordination (5 specialized agents)")
    print("• Enhanced temporal parameters and date ranges")
    print("• Source attribution with filing dates and accession numbers")
    print("• Comparative analysis across multiple companies and periods")

if __name__ == "__main__":
    asyncio.run(main())
